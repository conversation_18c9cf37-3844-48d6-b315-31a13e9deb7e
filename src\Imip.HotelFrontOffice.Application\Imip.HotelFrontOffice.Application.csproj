﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.HotelFrontOffice</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.HotelFrontOffice.Domain\Imip.HotelFrontOffice.Domain.csproj" />
    <ProjectReference Include="..\Imip.HotelFrontOffice.Application.Contracts\Imip.HotelFrontOffice.Application.Contracts.csproj" />
    <ProjectReference Include="..\Imip.HotelFrontOffice.EntityFrameworkCore\Imip.HotelFrontOffice.EntityFrameworkCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DinkToPdf" Version="1.0.8" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.3.0" />
    <PackageReference Include="EPPlus" Version="7.0.0" />
    <PackageReference Include="GdPicture.API" Version="14.3.6" />
    <PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" Version="*******" />
    <PackageReference Include="PDFsharp" Version="6.2.0" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="3.116.1" />
    <PackageReference Include="Syncfusion.DocIO.Net.Core" Version="29.2.5" />
    <PackageReference Include="Syncfusion.DocIORenderer.Net.Core" Version="29.2.5" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Account.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Reservations\ReservationsAppService.cs.new" />
  </ItemGroup>

</Project>
