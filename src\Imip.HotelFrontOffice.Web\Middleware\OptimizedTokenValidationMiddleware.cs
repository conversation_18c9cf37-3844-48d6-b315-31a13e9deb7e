using System;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Authentication;
using Imip.HotelFrontOffice.Web.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Web.Middleware;

/// <summary>
/// Optimized middleware for JWT token validation using caching
/// </summary>
public class OptimizedTokenValidationMiddleware : IMiddleware, ITransientDependency
{
    private readonly IJwtTokenCacheService _jwtTokenCache;
    private readonly ILogger<OptimizedTokenValidationMiddleware> _logger;

    public OptimizedTokenValidationMiddleware(
        IJwtTokenCacheService jwtTokenCache,
        ILogger<OptimizedTokenValidationMiddleware> logger)
    {
        _jwtTokenCache = jwtTokenCache;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // Skip token validation for non-API paths or paths that don't require authentication
        if (ShouldSkipValidation(context.Request.Path))
        {
            await next(context);
            return;
        }

        // Get the access token
        var accessToken = await context.GetAccessTokenWithFallbackAsync();

        if (string.IsNullOrEmpty(accessToken))
        {
            // No token found, let the authorization handlers handle this
            await next(context);
            return;
        }

        // Use cached token validation (much faster than parsing every time)
        var isValid = await _jwtTokenCache.IsTokenValidAsync(accessToken);

        if (!isValid)
        {
            // Return 401 Unauthorized with proper error response
            await SendUnauthorizedResponseAsync(context, "Token is invalid or expired");
            return;
        }

        // Store token info in HttpContext for downstream middleware
        var tokenInfo = await _jwtTokenCache.GetTokenInfoAsync(accessToken);
        if (tokenInfo != null)
        {
            context.Items["JwtTokenInfo"] = tokenInfo;
            context.Items["UserId"] = tokenInfo.UserId;
            context.Items["UserName"] = tokenInfo.UserName;
            context.Items["AccessToken"] = accessToken; // Store the actual token for permission checking

            _logger.LogDebug("Token validated and cached info stored for user {UserId}", tokenInfo.UserId);
        }

        // Token is valid, continue with the request
        await next(context);
    }

    private static bool ShouldSkipValidation(PathString path)
    {
        return !path.StartsWithSegments("/api") ||
               path.StartsWithSegments("/api/abp") ||
               path.StartsWithSegments("/swagger") ||
               path.StartsWithSegments("/health") ||
               path.StartsWithSegments("/connect/token");
    }

    private async Task SendUnauthorizedResponseAsync(HttpContext context, string errorMessage)
    {
        _logger.LogWarning("Token validation failed: {ErrorMessage}", errorMessage);

        context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "invalid_token",
            error_description = errorMessage
        };

        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }
}

/// <summary>
/// Extension methods for OptimizedTokenValidationMiddleware
/// </summary>
public static class OptimizedTokenValidationMiddlewareExtensions
{
    /// <summary>
    /// Adds the optimized token validation middleware to the pipeline
    /// </summary>
    /// <param name="builder">The application builder</param>
    /// <returns>The application builder</returns>
    public static IApplicationBuilder UseOptimizedTokenValidation(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<OptimizedTokenValidationMiddleware>();
    }
}
