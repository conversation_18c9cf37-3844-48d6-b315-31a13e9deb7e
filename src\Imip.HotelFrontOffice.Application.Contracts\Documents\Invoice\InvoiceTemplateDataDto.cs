using System;
using System.Collections.Generic;

namespace Imip.HotelFrontOffice.Documents.Invoice;

/// <summary>
/// DTO for invoice template data
/// </summary>
public class InvoiceTemplateDataDto
{
    /// <summary>
    /// The invoice number
    /// </summary>
    public string InvoiceNumber { get; set; } = string.Empty;

    /// <summary>
    /// The invoice date
    /// </summary>
    public DateTime InvoiceDate { get; set; }

    /// <summary>
    /// The payment code
    /// </summary>
    public string PaymentCode { get; set; } = string.Empty;

    /// <summary>
    /// The user who created the invoice
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// The transaction date
    /// </summary>
    public DateTime? TransactionDate { get; set; }

    /// <summary>
    /// The reservation code
    /// </summary>
    public string ReservationCode { get; set; } = string.Empty;

    /// <summary>
    /// The booker name
    /// </summary>
    public string BookerName { get; set; } = string.Empty;

    /// <summary>
    /// The guest name
    /// </summary>
    public string GuestName { get; set; } = string.Empty;

    /// <summary>
    /// The payment method
    /// </summary>
    public string PaymentMethod { get; set; } = string.Empty;

    /// <summary>
    /// The payment status
    /// </summary>
    public string PaymentStatus { get; set; } = string.Empty;

    /// <summary>
    /// The total amount
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// The paid amount
    /// </summary>
    public decimal PaidAmount { get; set; }

    /// <summary>
    /// The VAT rate (percentage)
    /// </summary>
    public decimal? VatRate { get; set; }

    /// <summary>
    /// The VAT amount
    /// </summary>
    public decimal? VatAmount { get; set; }

    /// <summary>
    /// The grand total (including VAT)
    /// </summary>
    public decimal GrantTotal { get; set; }

    /// <summary>
    /// The settlement company name (populated when VAT > 0)
    /// </summary>
    public string SettlementCompany { get; set; } = string.Empty;

    /// <summary>
    /// The payment details
    /// </summary>
    public List<InvoicePaymentDetailDto> PaymentDetails { get; set; } = new();

    /// <summary>
    /// The company name
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// The company address
    /// </summary>
    public string CompanyAddress { get; set; } = string.Empty;

    /// <summary>
    /// The company phone
    /// </summary>
    public string CompanyPhone { get; set; } = string.Empty;

    /// <summary>
    /// The company email
    /// </summary>
    public string CompanyEmail { get; set; } = string.Empty;

    /// <summary>
    /// The company logo URL
    /// </summary>
    public string CompanyLogoUrl { get; set; } = string.Empty;

    /// <summary>
    /// The check-in date
    /// </summary>
    public DateTime? CheckInDate { get; set; }

    /// <summary>
    /// The check-out date
    /// </summary>
    public DateTime? CheckOutDate { get; set; }

    /// <summary>
    /// The room number
    /// </summary>
    public string RoomNumber { get; set; } = string.Empty;
}

/// <summary>
/// DTO for invoice payment detail
/// </summary>
public class InvoicePaymentDetailDto
{
    /// <summary>
    /// The source type
    /// </summary>
    public string SourceType { get; set; } = string.Empty;
    public DateTime? CheckInDate { get; set; }
    public DateTime? CheckOutDate { get; set; }
    public string RoomNumber { get; set; } = string.Empty;

    /// <summary>
    /// The source ID
    /// </summary>
    public Guid SourceId { get; set; } = Guid.Empty;

    /// <summary>
    /// The description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// The amount
    /// </summary>
    public decimal Amount { get; set; }
    public decimal Qty { get; set; }
}
