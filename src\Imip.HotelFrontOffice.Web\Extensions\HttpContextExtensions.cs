﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;

namespace Imip.HotelFrontOffice.Web.Extensions;

public static class HttpContextExtensions
{
    /// <summary>
    /// Gets the access token from the HttpContext, first trying the optimized middleware context items,
    /// then the authentication properties, then falling back to the Authorization header if needed.
    /// </summary>
    /// <param name="httpContext">The HttpContext</param>
    /// <returns>The access token, or null if not found</returns>
    public static async Task<string?> GetAccessTokenWithFallbackAsync(this HttpContext httpContext)
    {
        // First try to get the token from the optimized middleware context items
        if (httpContext.Items.TryGetValue("AccessToken", out var tokenFromContext) &&
            tokenFromContext is string contextToken && !string.IsNullOrEmpty(contextToken))
        {
            return contextToken;
        }

        // Then try to get the token from the authentication properties
        var accessToken = await httpContext.GetTokenAsync("access_token");

        // If the token is not found in the authentication properties, try to extract it from the Authorization header
        if (string.IsNullOrEmpty(accessToken))
        {
            var authHeader = httpContext.Request.Headers.Authorization.FirstOrDefault();
            if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                accessToken = authHeader["Bearer ".Length..].Trim();
            }
        }

        return accessToken;
    }
}