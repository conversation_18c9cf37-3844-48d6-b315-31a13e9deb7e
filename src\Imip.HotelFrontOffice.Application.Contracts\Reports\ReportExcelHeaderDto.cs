using System;
using System.Collections.Generic;

namespace Imip.HotelFrontOffice.Reports;

public class ReportExcelHeaderDto
{

    public string Title { get; set; } = default!;
    public string SubTitle { get; set; } = default!;
    public List<ReportExcelHeaderRowDto> HeaderRows { get; set; } = new();
    public ReportExcelStyleDto TitleStyle { get; set; } = default!;
    public ReportExcelStyleDto SubTitleStyle { get; set; } = default!;
    public ReportExcelStyleDto HeaderStyle { get; set; } = default!;
    public ReportExcelStyleDto DataStyle { get; set; } = default!;
    public bool ShowGeneratedDate { get; set; } = true;
    public string DateFormat { get; set; } = "yyyy-MM-dd HH:mm:ss";
}

public class ReportExcelHeaderRowDto
{
    public List<ReportExcelHeaderCellDto> Cells { get; set; } = new();
}

public class ReportExcelHeaderCellDto
{
    public string Text { get; set; } = default!;
    public int ColSpan { get; set; } = 1;
    public int RowSpan { get; set; } = 1;
    public ReportExcelStyleDto Style { get; set; } = default!;
}

public class ReportExcelStyleDto
{
    public string FontName { get; set; } = "Arial";
    public int FontSize { get; set; } = 10;
    public bool Bold { get; set; }
    public string BackgroundColor { get; set; } = default!; // Hex color
    public string FontColor { get; set; } = default!; // Hex color
    public string HorizontalAlignment { get; set; } = "Left"; // Left, Center, Right
    public string VerticalAlignment { get; set; } = "Top"; // Top, Middle, Bottom
    public bool Border { get; set; }
    public bool WrapText { get; set; }
}
