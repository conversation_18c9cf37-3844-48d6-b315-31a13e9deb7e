﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class AddColumnToReport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ReportGroup",
                table: "MasterReports");

            migrationBuilder.AlterColumn<string>(
                name: "Query",
                table: "MasterReports",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExcelHeaderConfig",
                table: "MasterReports",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "MasterReports",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Parameters",
                table: "MasterReports",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "QueryType",
                table: "MasterReports",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Roles",
                table: "MasterReports",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExcelHeaderConfig",
                table: "MasterReports");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "MasterReports");

            migrationBuilder.DropColumn(
                name: "Parameters",
                table: "MasterReports");

            migrationBuilder.DropColumn(
                name: "QueryType",
                table: "MasterReports");

            migrationBuilder.DropColumn(
                name: "Roles",
                table: "MasterReports");

            migrationBuilder.AlterColumn<string>(
                name: "Query",
                table: "MasterReports",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<string>(
                name: "ReportGroup",
                table: "MasterReports",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }
    }
}
