﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.Guests;

public class CreateUpdateGuestDto
{
    [Required]
    [StringLength(128)]
    public string Fullname { get; set; } = default!;
    [Required]
    [StringLength(128)]
    public string IdentityNumber { get; set; } = default!;
    [StringLength(128)]
    public string PhoneNumber { get; set; } = default!;
    [StringLength(128)]
    public string Email { get; set; } = default!;
    [Required]
    [StringLength(128)]
    public string Nationality { get; set; } = default!;
    [StringLength(128)]
    public string CompanyName { get; set; } = default!;
    [StringLength(128)]
    public string Attachment { get; set; } = default!;
    [Required]
    public Guid? StatusId { get; set; } = default!;

    /// <summary>
    /// Array of attachments for the guest (e.g., identification documents, photos)
    /// </summary>
    public List<GuestAttachmentDto>? Attachments { get; set; }

}