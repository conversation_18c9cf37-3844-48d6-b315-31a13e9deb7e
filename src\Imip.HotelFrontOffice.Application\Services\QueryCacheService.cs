using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.MasterStatuses;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Services;

/// <summary>
/// Service for caching frequently accessed query data to improve performance
/// </summary>
public class QueryCacheService : ITransientDependency
{
    private readonly IMemoryCache _cache;
    private readonly IRepository<MasterStatus, Guid> _masterStatusRepository;
    private readonly ILogger<QueryCacheService> _logger;

    private const int CacheExpirationMinutes = 30;
    private const string MasterStatusCacheKey = "MasterStatuses_All";
    private const string StatusByCodeCacheKeyPrefix = "MasterStatus_Code_";
    private const string StatusByDocTypeCacheKeyPrefix = "MasterStatus_DocType_";

    public QueryCacheService(
        IMemoryCache cache,
        IRepository<MasterStatus, Guid> masterStatusRepository,
        ILogger<QueryCacheService> logger)
    {
        _cache = cache;
        _masterStatusRepository = masterStatusRepository;
        _logger = logger;
    }

    /// <summary>
    /// Get all master statuses with caching
    /// </summary>
    public async Task<List<MasterStatus>> GetAllMasterStatusesAsync()
    {
        return await _cache.GetOrCreateAsync(MasterStatusCacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CacheExpirationMinutes);
            entry.Size = 10; // Master status list is larger, assign more units

            try
            {
                var statuses = await _masterStatusRepository.GetListAsync();
                _logger.LogDebug("Cached {Count} master statuses", statuses.Count);
                return statuses;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading master statuses for cache");
                return new List<MasterStatus>();
            }
        }) ?? new List<MasterStatus>();
    }

    /// <summary>
    /// Get master status by code with caching
    /// </summary>
    public async Task<MasterStatus?> GetMasterStatusByCodeAsync(string code)
    {
        if (string.IsNullOrEmpty(code))
            return null;

        var cacheKey = $"{StatusByCodeCacheKeyPrefix}{code.ToLowerInvariant()}";

        return await _cache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CacheExpirationMinutes);
            entry.Size = 1; // Single master status entry

            try
            {
                var status = await _masterStatusRepository.FirstOrDefaultAsync(
                    ms => ms.Code != null && ms.Code.ToLower() == code.ToLower());

                _logger.LogDebug("Cached master status for code: {Code}", code);
                return status;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading master status by code {Code} for cache", code);
                return null;
            }
        });
    }

    /// <summary>
    /// Get master statuses by document type with caching
    /// </summary>
    public async Task<List<MasterStatus>> GetMasterStatusesByDocTypeAsync(string docType)
    {
        if (string.IsNullOrEmpty(docType))
            return new List<MasterStatus>();

        var cacheKey = $"{StatusByDocTypeCacheKeyPrefix}{docType.ToLowerInvariant()}";

        return await _cache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CacheExpirationMinutes);
            entry.Size = 5; // List of master statuses by doc type

            try
            {
                var statuses = await _masterStatusRepository.GetListAsync(
                    ms => ms.DocType != null && ms.DocType.ToLower() == docType.ToLower());

                _logger.LogDebug("Cached {Count} master statuses for doc type: {DocType}", statuses.Count, docType);
                return statuses;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading master statuses by doc type {DocType} for cache", docType);
                return new List<MasterStatus>();
            }
        }) ?? new List<MasterStatus>();
    }

    /// <summary>
    /// Clear all cached data
    /// </summary>
    public void ClearCache()
    {
        try
        {
            // Remove specific cache entries
            _cache.Remove(MasterStatusCacheKey);

            // Note: For prefix-based cache clearing, you might need a more sophisticated approach
            // This is a simplified version
            _logger.LogInformation("Cache cleared successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
        }
    }

    /// <summary>
    /// Clear cache for specific master status code
    /// </summary>
    public void ClearMasterStatusCodeCache(string code)
    {
        if (string.IsNullOrEmpty(code))
            return;

        try
        {
            var cacheKey = $"{StatusByCodeCacheKeyPrefix}{code.ToLowerInvariant()}";
            _cache.Remove(cacheKey);
            _logger.LogDebug("Cleared cache for master status code: {Code}", code);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache for master status code {Code}", code);
        }
    }

    /// <summary>
    /// Clear cache for specific document type
    /// </summary>
    public void ClearMasterStatusDocTypeCache(string docType)
    {
        if (string.IsNullOrEmpty(docType))
            return;

        try
        {
            var cacheKey = $"{StatusByDocTypeCacheKeyPrefix}{docType.ToLowerInvariant()}";
            _cache.Remove(cacheKey);
            _logger.LogDebug("Cleared cache for master status doc type: {DocType}", docType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache for master status doc type {DocType}", docType);
        }
    }
}
