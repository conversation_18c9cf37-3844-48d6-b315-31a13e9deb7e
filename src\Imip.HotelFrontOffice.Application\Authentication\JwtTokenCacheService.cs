using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Authentication;

/// <summary>
/// Service for caching JWT token information to improve performance
/// </summary>
public class JwtTokenCacheService : IJwtTokenCacheService, ITransientDependency
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;
    private readonly ILogger<JwtTokenCacheService> _logger;

    private static readonly TimeSpan MemoryCacheExpiration = TimeSpan.FromMinutes(5);
    private static readonly TimeSpan DistributedCacheExpiration = TimeSpan.FromMinutes(15);

    public JwtTokenCacheService(
        IMemoryCache memoryCache,
        IDistributedCache distributedCache,
        ILogger<JwtTokenCacheService> logger)
    {
        _memoryCache = memoryCache;
        _distributedCache = distributedCache;
        _logger = logger;
    }

    public async Task<JwtTokenInfo?> GetTokenInfoAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
            return null;

        var tokenHash = ComputeTokenHash(token);
        var cacheKey = $"jwt_token_info_{tokenHash}";

        // Check memory cache first (fastest)
        if (_memoryCache.TryGetValue(cacheKey, out JwtTokenInfo? cachedInfo))
        {
            _logger.LogDebug("JWT token info retrieved from memory cache");
            return cachedInfo;
        }

        // Check distributed cache
        try
        {
            var distributedValue = await _distributedCache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(distributedValue))
            {
                var tokenInfo = JsonSerializer.Deserialize<JwtTokenInfo>(distributedValue);

                // Store in memory cache for faster access
                _memoryCache.Set(cacheKey, tokenInfo, new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = MemoryCacheExpiration,
                    Size = 1 // Each token info entry counts as 1 unit
                });

                _logger.LogDebug("JWT token info retrieved from distributed cache");
                return tokenInfo;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve JWT token info from distributed cache");
        }

        // Parse token and cache result
        var parsedInfo = ParseJwtToken(token);
        if (parsedInfo != null)
        {
            parsedInfo.TokenHash = tokenHash;

            // Cache in both memory and distributed cache
            _memoryCache.Set(cacheKey, parsedInfo, new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = MemoryCacheExpiration,
                Size = 1 // Each token info entry counts as 1 unit
            });

            try
            {
                var serialized = JsonSerializer.Serialize(parsedInfo);
                await _distributedCache.SetStringAsync(cacheKey, serialized, new DistributedCacheEntryOptions
                {
                    SlidingExpiration = DistributedCacheExpiration
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to cache JWT token info in distributed cache");
            }

            _logger.LogDebug("JWT token parsed and cached");
        }

        return parsedInfo;
    }

    public async Task<bool> IsTokenValidAsync(string token)
    {
        var tokenInfo = await GetTokenInfoAsync(token);
        return tokenInfo?.IsValid == true;
    }

    public async Task<ClaimsPrincipal?> GetPrincipalAsync(string token)
    {
        var tokenInfo = await GetTokenInfoAsync(token);
        if (tokenInfo?.IsValid != true)
            return null;

        // Convert ClaimInfo objects back to Claim objects
        var claims = tokenInfo.Claims.Select(c => new Claim(c.Type, c.Value, c.ValueType, c.Issuer)).ToList();
        var identity = new ClaimsIdentity(claims, "jwt");
        return new ClaimsPrincipal(identity);
    }

    public async Task InvalidateTokenAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
            return;

        var tokenHash = ComputeTokenHash(token);
        var cacheKey = $"jwt_token_info_{tokenHash}";

        _memoryCache.Remove(cacheKey);

        try
        {
            await _distributedCache.RemoveAsync(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to invalidate JWT token in distributed cache");
        }
    }

    private JwtTokenInfo? ParseJwtToken(string token)
    {
        try
        {
            var jwtHandler = new JwtSecurityTokenHandler();

            if (!jwtHandler.CanReadToken(token))
            {
                _logger.LogWarning("Invalid JWT token format");
                return null;
            }

            var jwtToken = jwtHandler.ReadJwtToken(token);
            var currentTime = DateTime.UtcNow;

            // Extract claims
            var userIdClaim = jwtToken.Claims.FirstOrDefault(c =>
                c.Type == "sub" || c.Type == "user_id" || c.Type == ClaimTypes.NameIdentifier);
            var userNameClaim = jwtToken.Claims.FirstOrDefault(c =>
                c.Type == "preferred_username" || c.Type == "unique_name" || c.Type == ClaimTypes.Name);
            var emailClaim = jwtToken.Claims.FirstOrDefault(c =>
                c.Type == "email" || c.Type == ClaimTypes.Email);

            // Check expiration
            var isValid = true;
            var expiresAt = DateTime.MaxValue;
            var notBefore = DateTime.MinValue;
            var issuedAt = DateTime.MinValue;

            if (jwtToken.ValidTo != DateTime.MinValue)
            {
                expiresAt = jwtToken.ValidTo;
                if (expiresAt <= currentTime)
                {
                    isValid = false;
                    _logger.LogDebug("JWT token has expired");
                }
            }

            if (jwtToken.ValidFrom != DateTime.MinValue)
            {
                notBefore = jwtToken.ValidFrom;
                if (notBefore > currentTime)
                {
                    isValid = false;
                    _logger.LogDebug("JWT token is not yet valid");
                }
            }

            if (jwtToken.IssuedAt != DateTime.MinValue)
            {
                issuedAt = jwtToken.IssuedAt;
            }

            return new JwtTokenInfo
            {
                UserId = userIdClaim?.Value ?? string.Empty,
                UserName = userNameClaim?.Value ?? string.Empty,
                Email = emailClaim?.Value ?? string.Empty,
                ExpiresAt = expiresAt,
                NotBefore = notBefore,
                IssuedAt = issuedAt,
                Claims = jwtToken.Claims.Select(c => new ClaimInfo
                {
                    Type = c.Type,
                    Value = c.Value,
                    ValueType = c.ValueType,
                    Issuer = c.Issuer
                }).ToList(),
                IsValid = isValid
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing JWT token");
            return null;
        }
    }

    private static string ComputeTokenHash(string token)
    {
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(token));
        return Convert.ToBase64String(hashBytes)[..16]; // Use first 16 characters for cache key
    }
}
