﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Validation;

namespace Imip.HotelFrontOffice.ReservationFoodAndBeverages;

[Authorize(WismaAppPermissions.PolicyReservationFoodAndBeverages.Default)]
public class ReservationFoodAndBeveragesAppService : IdentityServerCrudAppService<
        ReservationFoodAndBeverage,
        ReservationFoodAndBeveragesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateReservationFoodAndBeveragesDto,
        CreateUpdateReservationFoodAndBeveragesDto
    >, IReservationFoodAndBeveragesAppService
{
    public ReservationFoodAndBeveragesAppService(IRepository<ReservationFoodAndBeverage, Guid> repository, ILogger<ReservationFoodAndBeveragesAppService> logger)
        : base(repository, logger)
    {
    }

    protected override async Task<IQueryable<ReservationFoodAndBeverage>> CreateFilteredQueryAsync(PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.ReservationDetails)
            .Include(x => x.FoodAndBeverage);
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyReservationFoodAndBeverages.View)]
    public override Task<PagedResultDto<ReservationFoodAndBeveragesDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyReservationFoodAndBeverages.View)]
    public override async Task<ReservationFoodAndBeveragesDto> GetAsync(Guid id)
    {
        var query = await Repository.GetQueryableAsync();
        var reservationFoodAndBeverage = await query
            .Include(x => x.ReservationDetails)
            .Include(x => x.FoodAndBeverage)
            .Include(x => x.PaymentStatus)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (reservationFoodAndBeverage is null)
        {
            throw new EntityNotFoundException(typeof(ReservationFoodAndBeverage), id);
        }

        return ObjectMapper.Map<ReservationFoodAndBeverage, ReservationFoodAndBeveragesDto>(reservationFoodAndBeverage);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyReservationFoodAndBeverages.Create)]
    public override Task<ReservationFoodAndBeveragesDto> CreateAsync(CreateUpdateReservationFoodAndBeveragesDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyReservationFoodAndBeverages.Edit)]
    public override Task<ReservationFoodAndBeveragesDto> UpdateAsync(Guid id, CreateUpdateReservationFoodAndBeveragesDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpPost("create-many")]
    [Authorize(WismaAppPermissions.PolicyReservationFoodAndBeverages.Create)]
    public async Task<ReservationFoodAndBeveragesDto> CreateManyAsync([FromBody] List<CreateUpdateReservationFoodAndBeveragesDto> items)
    {
        // await CheckCreatePolicyAsync();

        // Validate inputs
        if (items == null || items.Count == 0)
        {
            throw new AbpValidationException("No items provided for creation");
        }

        var entitiesToInsert = new List<ReservationFoodAndBeverage>();
        var entitiesToUpdate = new List<ReservationFoodAndBeverage>();
        var allEntities = new List<ReservationFoodAndBeverage>();

        foreach (var item in items)
        {
            // Validate each item
            if (item.ReservationDetailsId == Guid.Empty)
            {
                throw new AbpValidationException("ReservationDetailsId is required");
            }

            if (item.FoodAndBeverageId == Guid.Empty)
            {
                throw new AbpValidationException("FoodAndBeverageId is required");
            }

            if (item.Quantity <= 0)
            {
                throw new AbpValidationException("Quantity must be greater than 0");
            }

            if (item.TotalPrice < 0)
            {
                throw new AbpValidationException("TotalPrice must be greater than or equal to 0");
            }

            // Check if the item has an ID and if it exists in the database
            if (item.Id != Guid.Empty)
            {
                try
                {
                    // Try to get the existing entity
                    var existingEntity = await GetEntityByIdAsync(item.Id);

                    // Update the existing entity
                    await MapToEntityAsync(item, existingEntity);
                    entitiesToUpdate.Add(existingEntity);
                    allEntities.Add(existingEntity);
                }
                catch (EntityNotFoundException)
                {
                    // If entity with the provided ID doesn't exist, create a new one
                    var newEntity = await MapToEntityAsync(item);
                    entitiesToInsert.Add(newEntity);
                    allEntities.Add(newEntity);
                }
            }
            else
            {
                // Create a new entity if no ID is provided
                var newEntity = await MapToEntityAsync(item);
                entitiesToInsert.Add(newEntity);
                allEntities.Add(newEntity);
            }
        }

        // Insert new entities
        if (entitiesToInsert.Count > 0)
        {
            await Repository.InsertManyAsync(entitiesToInsert, autoSave: true);
        }

        // Update existing entities
        if (entitiesToUpdate.Count > 0)
        {
            await Repository.UpdateManyAsync(entitiesToUpdate, autoSave: true);
        }

        // Return the first item as a sample of what was created/updated
        var firstEntity = allEntities.FirstOrDefault();
        if (firstEntity == null)
        {
            // Return an empty DTO if no entities were created/updated
            return new ReservationFoodAndBeveragesDto();
        }

        return await MapToGetOutputDtoAsync(firstEntity);
    }

    [HttpPut("update-many")]
    [Authorize(WismaAppPermissions.PolicyReservationFoodAndBeverages.Edit)]
    public async Task<ReservationFoodAndBeveragesDto> UpdateManyAsync([FromBody] List<CreateUpdateReservationFoodAndBeveragesDto> items)
    {
        // await CheckUpdatePolicyAsync();

        // Validate inputs
        if (items == null || items.Count == 0)
        {
            throw new AbpValidationException("No items provided for update");
        }

        var entities = new List<ReservationFoodAndBeverage>();

        foreach (var item in items)
        {
            // Validate each item
            if (item.Id == Guid.Empty)
            {
                throw new AbpValidationException("Id is required for updates");
            }

            if (item.ReservationDetailsId == Guid.Empty)
            {
                throw new AbpValidationException("ReservationDetailsId is required");
            }

            if (item.FoodAndBeverageId == Guid.Empty)
            {
                throw new AbpValidationException("FoodAndBeverageId is required");
            }

            if (item.Quantity <= 0)
            {
                throw new AbpValidationException("Quantity must be greater than 0");
            }

            if (item.TotalPrice < 0)
            {
                throw new AbpValidationException("TotalPrice must be greater than or equal to 0");
            }

            // For bulk updates, we assume each item has an Id property
            try
            {
                var entity = await GetEntityByIdAsync(item.Id);
                await MapToEntityAsync(item, entity);
                entities.Add(entity);
            }
            catch (EntityNotFoundException)
            {
                throw new AbpValidationException($"Entity with Id {item.Id} not found");
            }
        }

        await Repository.UpdateManyAsync(entities, autoSave: true);

        // Return the first item as a sample of what was updated
        var firstEntity = entities.FirstOrDefault();
        if (firstEntity == null)
        {
            // Return an empty DTO if no entities were updated
            return new ReservationFoodAndBeveragesDto();
        }

        return await MapToGetOutputDtoAsync(firstEntity);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyReservationFoodAndBeverages.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}