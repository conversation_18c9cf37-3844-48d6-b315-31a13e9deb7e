﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.ReservationTypes;

[Authorize(WismaAppPermissions.PolicyReservationType.Default)]
public class ReservationTypesAppService : IdentityServerCrudAppService<
        ReservationType,
        ReservationTypesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateReservationTypesDto,
        CreateUpdateReservationTypesDto
    >, IReservationTypesAppService
{
    private readonly IRepository<ReservationType, Guid> _repository;

    public ReservationTypesAppService(IRepository<ReservationType, Guid> repository, ILogger<ReservationTypesAppService> logger)
        : base(repository, logger)
    {
        _repository = repository;
    }

    [Authorize(WismaAppPermissions.PolicyReservationType.Create)]
    public override async Task<ReservationTypesDto> CreateAsync(CreateUpdateReservationTypesDto input)
    {
        var entity = new ReservationType(
            GuidGenerator.Create(),
            input.Name,
            input.StatusId
        );

        await _repository.InsertAsync(entity);
        return ObjectMapper.Map<ReservationType, ReservationTypesDto>(entity);
    }

    [Authorize(WismaAppPermissions.PolicyReservationType.Edit)]
    public override async Task<ReservationTypesDto> UpdateAsync(Guid id, CreateUpdateReservationTypesDto input)
    {
        var entity = await _repository.GetAsync(id);

        entity.Name = input.Name;
        entity.StatusId = input.StatusId;

        await _repository.UpdateAsync(entity);
        return ObjectMapper.Map<ReservationType, ReservationTypesDto>(entity);
    }
}