using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Authentication;
using Imip.HotelFrontOffice.Users;
using Imip.HotelFrontOffice.Web.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Web.Middleware;

/// <summary>
/// Optimized middleware for user synchronization using cached token information
/// </summary>
public class OptimizedUserSynchronizationMiddleware : IMiddleware, ITransientDependency
{
    private readonly ILogger<OptimizedUserSynchronizationMiddleware> _logger;

    public OptimizedUserSynchronizationMiddleware(ILogger<OptimizedUserSynchronizationMiddleware> logger)
    {
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            // Skip synchronization for certain paths
            if (ShouldSkipSynchronization(context.Request.Path))
            {
                await next(context);
                return;
            }

            // Check if we already have token info from previous middleware
            if (context.Items.TryGetValue("JwtTokenInfo", out var tokenInfoObj) &&

                tokenInfoObj is JwtTokenInfo tokenInfo)
            {
                // Use cached token info instead of parsing again
                await SynchronizeUserFromTokenInfoAsync(context, tokenInfo);
            }
            else
            {
                // Fallback to token extraction (should be rare with optimized pipeline)
                var accessToken = await context.GetAccessTokenWithFallbackAsync();
                if (!string.IsNullOrEmpty(accessToken))
                {
                    await SynchronizeUserFromTokenAsync(context, accessToken);
                }
            }

            await next(context);
        }
        catch (Exception ex)
        {
            // Log the error but don't break the request pipeline
            _logger.LogError(ex, "Error in OptimizedUserSynchronizationMiddleware");

            // Continue with the pipeline - user synchronization failure shouldn't break the request
            await next(context);
        }
    }

    private async Task SynchronizeUserFromTokenInfoAsync(HttpContext context, JwtTokenInfo tokenInfo)
    {
        if (!tokenInfo.IsValid || string.IsNullOrEmpty(tokenInfo.UserId))
        {
            _logger.LogDebug("Invalid token info, skipping user synchronization");
            return;
        }

        try
        {
            // Get the user synchronization service
            using var scope = context.RequestServices.CreateScope();
            var userSyncService = scope.ServiceProvider.GetRequiredService<IUserSynchronizationService>();

            // Check if user exists using optimized cache
            if (Guid.TryParse(tokenInfo.UserId, out var userGuid))
            {
                var userExists = await userSyncService.UserExistsAsync(userGuid);

                if (!userExists)
                {
                    _logger.LogInformation("User {UserId} not found in internal database, synchronizing from token", tokenInfo.UserId);

                    // We need the actual token for synchronization, try to get it from context
                    var accessToken = await context.GetAccessTokenWithFallbackAsync();
                    if (!string.IsNullOrEmpty(accessToken))
                    {
                        var synchronizedUser = await userSyncService.SynchronizeUserFromTokenAsync(accessToken);
                        _logger.LogInformation("Successfully synchronized user {UserId} ({UserName}) to internal database",
                            synchronizedUser.Id, synchronizedUser.UserName);

                        // Store synchronized user info in context for downstream use
                        context.Items["SynchronizedUser"] = synchronizedUser;
                    }
                }
                else
                {
                    _logger.LogDebug("User {UserId} already exists, skipping synchronization", tokenInfo.UserId);
                }
            }
            else
            {
                _logger.LogWarning("Invalid user ID format in token: {UserId}", tokenInfo.UserId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing user {UserId} from token info", tokenInfo.UserId);
        }
    }

    private async Task SynchronizeUserFromTokenAsync(HttpContext context, string token)
    {
        try
        {
            // Get the user synchronization service
            using var scope = context.RequestServices.CreateScope();
            var userSyncService = scope.ServiceProvider.GetRequiredService<IUserSynchronizationService>();

            // This will use the optimized service with caching
            var synchronizedUser = await userSyncService.SynchronizeUserFromTokenAsync(token);


            if (synchronizedUser != null)
            {
                _logger.LogDebug("User synchronized from token: {UserId}", synchronizedUser.Id);
                context.Items["SynchronizedUser"] = synchronizedUser;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing user from token");
        }
    }

    private static bool ShouldSkipSynchronization(PathString path)
    {
        return !path.StartsWithSegments("/api") ||
               path.StartsWithSegments("/api/abp") ||
               path.StartsWithSegments("/swagger") ||
               path.StartsWithSegments("/health") ||
               path.StartsWithSegments("/connect/token");
    }
}

/// <summary>
/// Extension methods for OptimizedUserSynchronizationMiddleware
/// </summary>
public static class OptimizedUserSynchronizationMiddlewareExtensions
{
    /// <summary>
    /// Adds the optimized user synchronization middleware to the pipeline
    /// </summary>
    /// <param name="builder">The application builder</param>
    /// <returns>The application builder</returns>
    public static IApplicationBuilder UseOptimizedUserSynchronization(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<OptimizedUserSynchronizationMiddleware>();
    }
}
