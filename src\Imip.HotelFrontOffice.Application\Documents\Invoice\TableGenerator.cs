using System;
using System.Collections.Generic;
using System.Linq;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.Extensions.Configuration;

namespace Imip.HotelFrontOffice.Documents.Invoice;

/// <summary>
/// Utility class for generating tables in Word documents
/// </summary>
public static class TableGenerator
{
    /// <summary>
    /// Creates a table in a Word document
    /// </summary>
    /// <param name="wordDocument">The Word document</param>
    /// <param name="tableData">The table data</param>
    /// <param name="fontSize">Font size to use for the table (default: 9)</param>
    /// <param name="configuration">Configuration for font weight settings (optional)</param>
    /// <returns>The created table</returns>
    public static Table CreateTable(WordprocessingDocument wordDocument, TableGenerationDto tableData, int fontSize = 9, IConfiguration? configuration = null)
    {
        // Create a new table
        var table = new Table();

        // Get table properties from the DTO or use defaults
        var props = tableData.TableProperties ?? new TablePropertiesDto();

        // Set table properties with enhanced PDF compatibility
        var tableProperties = new TableProperties();

        // Set table width
        tableProperties.AppendChild(new TableWidth
        {
            Width = props.Width.ToString(),
            Type = GetTableWidthType(props.WidthType)
        });

        // Set table borders with explicit color and space for better PDF rendering
        var borderColor = "000000"; // Black
        tableProperties.AppendChild(new TableBorders(
            new TopBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = (uint)props.BorderSize,
                Color = borderColor,
                Space = 0
            },
            new BottomBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = (uint)props.BorderSize,
                Color = borderColor,
                Space = 0
            },
            new LeftBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = (uint)props.BorderSize,
                Color = borderColor,
                Space = 0
            },
            new RightBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = (uint)props.BorderSize,
                Color = borderColor,
                Space = 0
            },
            new InsideHorizontalBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = (uint)props.BorderSize,
                Color = borderColor,
                Space = 0
            },
            new InsideVerticalBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = (uint)props.BorderSize,
                Color = borderColor,
                Space = 0
            }
        ));

        // Set table layout - use Fixed for better PDF rendering
        tableProperties.AppendChild(new TableLayout
        {
            Type = TableLayoutValues.Fixed
        });

        // Add table look for better PDF compatibility
        tableProperties.AppendChild(new TableLook
        {
            Val = "04A0",
            FirstRow = true,
            LastRow = true,
            FirstColumn = true,
            LastColumn = true,
            NoHorizontalBand = false,
            NoVerticalBand = true
        });

        // Add table style for better PDF rendering
        tableProperties.AppendChild(new TableStyle { Val = "TableGrid" });

        // Add table caption for better PDF structure
        tableProperties.AppendChild(new TableCaption { Val = "Invoice Table" });

        // Set cell spacing if specified
        if (props.CellSpacing > 0)
        {
            tableProperties.AppendChild(new TableCellSpacing
            {
                Width = props.CellSpacing.ToString(),
                Type = TableWidthUnitValues.Dxa
            });
        }

        table.AppendChild(tableProperties);

        // Add header row if headers are provided
        if (tableData.Headers.Count > 0)
        {
            var headerRow = new TableRow();

            foreach (var header in tableData.Headers)
            {
                var cell = CreateTableCell(
                    header.Text,
                    true,
                    TableCellAlignment.Center,
                    header.ColSpan,
                    1,
                    TableCellAlignment.Center,
                    fontSize,
                    header.WidthPercentage,
                    configuration,
                    null, // No specific cell DTO for headers
                    tableData.TableProperties);

                headerRow.AppendChild(cell);
            }

            table.AppendChild(headerRow);
        }

        // Add data rows
        foreach (var row in tableData.Rows)
        {
            var tableRow = new TableRow();

            foreach (var cell in row.Cells)
            {
                // Use cell-specific font size if available, otherwise use default
                int cellFontSize = cell.FontSize ?? fontSize;

                tableRow.AppendChild(CreateTableCell(
                    cell.Text,
                    cell.IsHeader || cell.IsBold,
                    cell.HorizontalAlignment,
                    cell.ColSpan,
                    cell.RowSpan,
                    cell.VerticalAlignment,
                    cellFontSize,
                    cell.WidthPercentage,
                    configuration,
                    cell, // Pass the cell DTO for margin configuration
                    tableData.TableProperties));
            }

            table.AppendChild(tableRow);
        }

        return table;
    }

    /// <summary>
    /// Creates a table cell
    /// </summary>
    private static TableCell CreateTableCell(
        string text,
        bool isBold,
        TableCellAlignment horizontalAlignment = TableCellAlignment.Left,
        int colSpan = 1,
        int rowSpan = 1,
        TableCellAlignment verticalAlignment = TableCellAlignment.Center,
        int fontSize = 9,
        double widthPercentage = 0,
        IConfiguration? configuration = null,
        TableCellDto? cellDto = null,
        TablePropertiesDto? tableProperties = null)
    {
        var cell = new TableCell();

        // Set cell properties
        var cellProperties = new TableCellProperties();

        // Set grid span (for column merging)
        if (colSpan > 1)
        {
            cellProperties.AppendChild(new GridSpan { Val = colSpan });
        }

        // Set vertical merge (for row merging)
        if (rowSpan > 1)
        {
            cellProperties.AppendChild(new VerticalMerge { Val = MergedCellValues.Restart });
        }
        else if (rowSpan < 0) // Continuation of a vertical merge
        {
            cellProperties.AppendChild(new VerticalMerge { Val = MergedCellValues.Continue });
        }

        // Set cell width if specified - always set width for better PDF rendering
        if (widthPercentage > 0)
        {
            cellProperties.AppendChild(new TableCellWidth
            {
                Type = TableWidthUnitValues.Pct,
                Width = ((int)(widthPercentage * 50)).ToString()
            });
        }
        else
        {
            // Default width if not specified
            cellProperties.AppendChild(new TableCellWidth
            {
                Type = TableWidthUnitValues.Pct,
                Width = "1000" // 20% (50 * 20)
            });
        }

        // Set cell borders explicitly for better PDF rendering
        // Use thicker borders for better visibility in PDF
        var borderColor = "000000"; // Black
        cellProperties.AppendChild(new TableCellBorders(
            new TopBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = 2,
                Color = borderColor,
                Space = 0
            },
            new BottomBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = 2,
                Color = borderColor,
                Space = 0
            },
            new LeftBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = 2,
                Color = borderColor,
                Space = 0
            },
            new RightBorder
            {
                Val = new EnumValue<BorderValues>(BorderValues.Single),
                Size = 2,
                Color = borderColor,
                Space = 0
            }
        ));

        // Set cell margins for padding control
        var leftMargin = cellDto?.LeftMargin ?? tableProperties?.DefaultCellLeftMargin ?? 108;
        var rightMargin = cellDto?.RightMargin ?? tableProperties?.DefaultCellRightMargin ?? 108;
        var topMargin = cellDto?.TopMargin ?? tableProperties?.DefaultCellTopMargin ?? 0;
        var bottomMargin = cellDto?.BottomMargin ?? tableProperties?.DefaultCellBottomMargin ?? 0;

        cellProperties.AppendChild(new TableCellMargin(
            new LeftMargin { Width = leftMargin.ToString(), Type = TableWidthUnitValues.Dxa },
            new RightMargin { Width = rightMargin.ToString(), Type = TableWidthUnitValues.Dxa },
            new TopMargin { Width = topMargin.ToString(), Type = TableWidthUnitValues.Dxa },
            new BottomMargin { Width = bottomMargin.ToString(), Type = TableWidthUnitValues.Dxa }
        ));

        // Set vertical alignment
        var vertAlign = TableVerticalAlignmentValues.Center;
        switch (verticalAlignment)
        {
            case TableCellAlignment.Top:
                vertAlign = TableVerticalAlignmentValues.Top;
                break;
            case TableCellAlignment.Bottom:
                vertAlign = TableVerticalAlignmentValues.Bottom;
                break;
        }
        cellProperties.AppendChild(new TableCellVerticalAlignment { Val = vertAlign });

        // Set horizontal alignment
        var justification = JustificationValues.Left;
        switch (horizontalAlignment)
        {
            case TableCellAlignment.Center:
                justification = JustificationValues.Center;
                break;
            case TableCellAlignment.Right:
                justification = JustificationValues.Right;
                break;
        }

        // Create run properties with font size and enhanced formatting for PDF
        var runProperties = new RunProperties();

        // Add bold if needed, but check configuration for lighter font weight in Kubernetes
        if (isBold)
        {
            var useLighterFontWeight = configuration?.GetValue<bool>("PdfOptimization:UseLighterFontWeight", false) ?? false;

            if (useLighterFontWeight)
            {
                // Use normal weight instead of bold for Kubernetes deployment
                // This helps with font rendering issues in containerized environments
                // The text will still be visually distinct due to other formatting
            }
            else
            {
                // Use bold as normal for non-Kubernetes environments
                runProperties.AppendChild(new Bold());
            }
        }

        // Set font size (in half-points, so 9pt = 18 half-points)
        runProperties.AppendChild(new FontSize { Val = (fontSize * 2).ToString() });

        // Set font family for better PDF rendering - use system defaults to avoid embedding large fonts
        // Only specify fonts if absolutely necessary to reduce PDF file size
        runProperties.AppendChild(new RunFonts
        {
            Ascii = "Arial", // Use Arial instead of Calibri for smaller size
            HighAnsi = "Arial",
            ComplexScript = "Arial"
            // Removed EastAsia font specification to prevent large font embedding
            // The system will use default fonts for Chinese characters
        });

        // Add paragraph properties with spacing for better PDF rendering
        var paragraphProperties = new ParagraphProperties(
            new Justification { Val = justification },
            new SpacingBetweenLines
            {
                Before = "0",
                After = "0",
                Line = "240", // Single line spacing
                LineRule = LineSpacingRuleValues.Auto
            },
            new ParagraphMarkRunProperties(
                new FontSize { Val = (fontSize * 2).ToString() }
            )
        );

        // Add cell content with enhanced properties
        var paragraph = new Paragraph(
            paragraphProperties,
            new Run(
                runProperties,
                new Text(text) { Space = SpaceProcessingModeValues.Preserve }
            )
        );

        cell.AppendChild(cellProperties);
        cell.AppendChild(paragraph);

        return cell;
    }

    /// <summary>
    /// Replaces a table placeholder in a Word document
    /// </summary>
    public static void ReplaceTablePlaceholder(WordprocessingDocument wordDocument, string placeholderText, TableGenerationDto tableData, int fontSize = 9, IConfiguration? configuration = null)
    {
        var mainPart = wordDocument.MainDocumentPart;
        if (mainPart == null) return;

        var document = mainPart.Document;
        if (document == null) return;

        // Find the table placeholder paragraph
        var tablePlaceholderParagraph = document.Descendants<Paragraph>()
            .FirstOrDefault(p => p.InnerText.Contains(placeholderText));

        if (tablePlaceholderParagraph == null) return;

        // Create the table
        var table = CreateTable(wordDocument, tableData, fontSize, configuration);

        // Replace the placeholder paragraph with the table
        var parent = tablePlaceholderParagraph.Parent;
        if (parent != null)
        {
            parent.InsertAfter(table, tablePlaceholderParagraph);
            tablePlaceholderParagraph.Remove();
        }
    }

    /// <summary>
    /// Converts a string width type to TableWidthUnitValues
    /// </summary>
    private static TableWidthUnitValues GetTableWidthType(string widthType)
    {
        return widthType.ToLower() switch
        {
            "pct" => TableWidthUnitValues.Pct,
            "dxa" => TableWidthUnitValues.Dxa,
            "auto" => TableWidthUnitValues.Auto,
            _ => TableWidthUnitValues.Pct
        };
    }
}
