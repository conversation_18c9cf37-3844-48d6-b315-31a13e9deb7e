﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.FoodAndBeverages;

[Authorize(WismaAppPermissions.PolicyFoodAndBeverage.Default)]
public class FoodAndBeverageAppService : CrudAppService<
    FoodAndBeverage,
    FoodAndBeverageDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateFoodAndBeverageDto,
    CreateUpdateFoodAndBeverageDto
>, IFoodAndBeverageAppService
{
    private readonly IRepository<FoodAndBeverage, Guid> _repository;
    public FoodAndBeverageAppService(IRepository<FoodAndBeverage, Guid> repository,
         ILogger<FoodAndBeverageAppService> logger)
        : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<IQueryable<FoodAndBeverage>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        var query = await Repository.GetQueryableAsync();
        return query.Include(x => x.TypeFoodAndBeverage)
            .OrderByDescending(x => x.CreationTime);
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyFoodAndBeverage.View)]
    public override Task<PagedResultDto<FoodAndBeverageDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyFoodAndBeverage.View)]
    public override Task<FoodAndBeverageDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }
    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyFoodAndBeverage.Create)]
    public override async Task<FoodAndBeverageDto> CreateAsync(CreateUpdateFoodAndBeverageDto input)
    {
        // await CheckCreatePolicyAsync();

        var entity = await MapToEntityAsync(input);

        // Set ExtraProperties for foreign names if provided in the input
        if (input.ExtraProperties != null)
        {
            foreach (var property in input.ExtraProperties)
            {
                entity.SetProperty(property.Key, property.Value);
            }
        }
        else
        {
            // Set default foreign names if not provided
            entity.SetProperty("ForeignName1", string.Empty);
            entity.SetProperty("ForeignName2", string.Empty);
        }

        await Repository.InsertAsync(entity);

        return await MapToGetOutputDtoAsync(entity);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyFoodAndBeverage.Edit)]
    public override async Task<FoodAndBeverageDto> UpdateAsync(Guid id, CreateUpdateFoodAndBeverageDto input)
    {
        // await CheckUpdatePolicyAsync();

        var entity = await Repository.GetAsync(id);

        await MapToEntityAsync(input, entity);

        // Update ExtraProperties for foreign names if provided in the input
        if (input.ExtraProperties != null)
        {
            foreach (var property in input.ExtraProperties)
            {
                entity.SetProperty(property.Key, property.Value);
            }
        }

        await Repository.UpdateAsync(entity);

        return await MapToGetOutputDtoAsync(entity);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyFoodAndBeverage.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}