using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Security.Claims;

namespace Imip.HotelFrontOffice.Permissions;

/// <summary>
/// Cached implementation of permission checker for improved performance
/// </summary>
public class CachedPermissionChecker : IPermissionChecker, ITransientDependency
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;
    private readonly IConfiguration _configuration;
    private readonly ILogger<CachedPermissionChecker> _logger;

    private static readonly TimeSpan MemoryCacheExpiration = TimeSpan.FromMinutes(2);
    private static readonly TimeSpan DistributedCacheExpiration = TimeSpan.FromMinutes(10);

    public CachedPermissionChecker(
        IMemoryCache memoryCache,
        IDistributedCache distributedCache,
        IHttpClientFactory httpClientFactory,
        IHttpContextAccessor httpContextAccessor,
        ICurrentPrincipalAccessor currentPrincipalAccessor,
        IConfiguration configuration,
        ILogger<CachedPermissionChecker> logger)
    {
        _memoryCache = memoryCache;
        _distributedCache = distributedCache;
        _httpClientFactory = httpClientFactory;
        _httpContextAccessor = httpContextAccessor;
        _currentPrincipalAccessor = currentPrincipalAccessor;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<bool> IsGrantedAsync(string name)
    {
        return await IsGrantedAsync(_currentPrincipalAccessor.Principal, name);
    }

    public async Task<bool> IsGrantedAsync(ClaimsPrincipal? claimsPrincipal, string name)
    {
        if (string.IsNullOrEmpty(name))
            return false;

        var userId = GetUserId(claimsPrincipal);
        if (string.IsNullOrEmpty(userId))
            return false;

        var cacheKey = $"permission_{userId}_{name}";

        // Check memory cache first (fastest - ~1ms)
        if (_memoryCache.TryGetValue(cacheKey, out bool isGranted))
        {
            _logger.LogDebug("Permission check from memory cache: {Permission} = {IsGranted}", name, isGranted);
            return isGranted;
        }

        // Check distributed cache (fast - ~5ms)
        try
        {
            var redisValue = await _distributedCache.GetStringAsync(cacheKey);
            if (redisValue != null)
            {
                isGranted = bool.Parse(redisValue);

                // Store in memory cache for even faster access

                _memoryCache.Set(cacheKey, isGranted, MemoryCacheExpiration);


                _logger.LogDebug("Permission check from distributed cache: {Permission} = {IsGranted}", name, isGranted);
                return isGranted;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check permission in distributed cache: {Permission}", name);
        }

        // Fallback to Identity Server call (slow - 50-200ms)
        isGranted = await CheckPermissionWithIdentityServerAsync(name);

        // Cache the result
        _memoryCache.Set(cacheKey, isGranted, new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = MemoryCacheExpiration,
            Size = 1 // Each permission result counts as 1 unit
        });


        try
        {
            await _distributedCache.SetStringAsync(cacheKey, isGranted.ToString(), new DistributedCacheEntryOptions
            {
                SlidingExpiration = DistributedCacheExpiration
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache permission result: {Permission}", name);
        }

        _logger.LogDebug("Permission check from Identity Server: {Permission} = {IsGranted}", name, isGranted);
        return isGranted;
    }

    public async Task<bool> IsGrantedAsync(Guid userId, string name)
    {
        // Create a cache key for user-specific permission
        var cacheKey = $"permission_{userId}_{name}";

        // Check memory cache first
        if (_memoryCache.TryGetValue(cacheKey, out bool isGranted))
        {
            _logger.LogDebug("Permission check from memory cache for user {UserId}: {Permission} = {IsGranted}", userId, name, isGranted);
            return isGranted;
        }

        // Check distributed cache
        try
        {
            var redisValue = await _distributedCache.GetStringAsync(cacheKey);
            if (redisValue != null)
            {
                isGranted = bool.Parse(redisValue);
                _memoryCache.Set(cacheKey, isGranted, new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = MemoryCacheExpiration,
                    Size = 1 // Each permission result counts as 1 unit
                });
                _logger.LogDebug("Permission check from distributed cache for user {UserId}: {Permission} = {IsGranted}", userId, name, isGranted);
                return isGranted;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check permission in distributed cache for user {UserId}: {Permission}", userId, name);
        }

        // Fallback to Identity Server call
        isGranted = await CheckPermissionWithIdentityServerForUserAsync(userId, name);

        // Cache the result
        _memoryCache.Set(cacheKey, isGranted, new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = MemoryCacheExpiration,
            Size = 1 // Each permission result counts as 1 unit
        });

        try
        {
            await _distributedCache.SetStringAsync(cacheKey, isGranted.ToString(), new DistributedCacheEntryOptions
            {
                SlidingExpiration = DistributedCacheExpiration
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache permission result for user {UserId}: {Permission}", userId, name);
        }

        _logger.LogDebug("Permission check from Identity Server for user {UserId}: {Permission} = {IsGranted}", userId, name, isGranted);
        return isGranted;
    }

    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(string[] names)
    {
        return await IsGrantedAsync(_currentPrincipalAccessor.Principal, names);
    }

    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(ClaimsPrincipal? principal, string[] names)
    {
        var result = new MultiplePermissionGrantResult();
        var userId = GetUserId(principal);


        if (string.IsNullOrEmpty(userId))
        {
            foreach (var name in names)
            {
                result.Result[name] = PermissionGrantResult.Prohibited;
            }
            return result;
        }

        var cachedResults = new Dictionary<string, bool>();
        var uncachedPermissions = new List<string>();

        // Check cache for all permissions first
        foreach (var permission in names)
        {
            var cacheKey = $"permission_{userId}_{permission}";


            if (_memoryCache.TryGetValue(cacheKey, out bool cached))
            {
                cachedResults[permission] = cached;
            }
            else
            {
                uncachedPermissions.Add(permission);
            }
        }

        // Batch check uncached permissions
        if (uncachedPermissions.Any())
        {
            var batchResults = await CheckMultiplePermissionsWithIdentityServerAsync(uncachedPermissions.ToArray());


            foreach (var batchResult in batchResults)
            {
                cachedResults[batchResult.Key] = batchResult.Value;

                // Cache individual results
                var cacheKey = $"permission_{userId}_{batchResult.Key}";
                _memoryCache.Set(cacheKey, batchResult.Value, new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = MemoryCacheExpiration,
                    Size = 1 // Each permission result counts as 1 unit
                });


                try
                {
                    await _distributedCache.SetStringAsync(cacheKey, batchResult.Value.ToString(),

                        new DistributedCacheEntryOptions { SlidingExpiration = DistributedCacheExpiration });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to cache batch permission result: {Permission}", batchResult.Key);
                }
            }
        }

        // Build final result
        foreach (var permission in names)
        {
            var isGranted = cachedResults.GetValueOrDefault(permission, false);
            result.Result[permission] = isGranted ? PermissionGrantResult.Granted : PermissionGrantResult.Prohibited;
        }

        return result;
    }

    private async Task<bool> CheckPermissionWithIdentityServerAsync(string permissionName)
    {
        try
        {
            var accessToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
                return false;

            var identityServerUrl = _configuration["AuthServer:Authority"];
            var permissionCheckEndpoint = $"{identityServerUrl}/api/permission-check";

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            var requestContent = new StringContent(
                JsonSerializer.Serialize(new { permission = permissionName }),
                Encoding.UTF8,
                "application/json");

            var response = await client.PostAsync(permissionCheckEndpoint, requestContent);


            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<PermissionCheckResponse>(responseContent);
                return result?.IsGranted == true;
            }

            _logger.LogWarning("Permission check failed with status: {StatusCode}", response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission with Identity Server: {Permission}", permissionName);
            return false;
        }
    }

    private async Task<Dictionary<string, bool>> CheckMultiplePermissionsWithIdentityServerAsync(string[] permissions)
    {
        var results = new Dictionary<string, bool>();


        try
        {
            var accessToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                foreach (var permission in permissions)
                    results[permission] = false;
                return results;
            }

            var identityServerUrl = _configuration["AuthServer:Authority"];
            var permissionCheckEndpoint = $"{identityServerUrl}/api/permission-check/multiple";

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            var requestContent = new StringContent(
                JsonSerializer.Serialize(new { permissions }),
                Encoding.UTF8,
                "application/json");

            var response = await client.PostAsync(permissionCheckEndpoint, requestContent);


            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<MultiplePermissionCheckResponse>(responseContent);


                if (result?.Results != null)
                {
                    foreach (var kvp in result.Results)
                    {
                        results[kvp.Key] = kvp.Value;
                    }
                }
            }
            else
            {
                _logger.LogWarning("Multiple permission check failed with status: {StatusCode}", response.StatusCode);
                foreach (var permission in permissions)
                    results[permission] = false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking multiple permissions with Identity Server");
            foreach (var permission in permissions)
                results[permission] = false;
        }

        return results;
    }

    private async Task<bool> CheckPermissionWithIdentityServerForUserAsync(Guid userId, string permissionName)
    {
        try
        {
            var accessToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
                return false;

            var identityServerUrl = _configuration["AuthServer:Authority"];
            var permissionCheckEndpoint = $"{identityServerUrl}/api/permission-check/user";

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            var response = await client.GetAsync($"{permissionCheckEndpoint}?userId={userId}&name={Uri.EscapeDataString(permissionName)}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<PermissionCheckResponse>(responseContent);
                return result?.IsGranted == true;
            }

            _logger.LogWarning("Permission check failed for user {UserId} with status: {StatusCode}", userId, response.StatusCode);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission with Identity Server for user {UserId}: {Permission}", userId, permissionName);
            return false;
        }
    }

    private string? GetUserId(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("sub")?.Value ??
               principal?.FindFirst("user_id")?.Value ??
               principal?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }

    private async Task<string?> GetAccessTokenAsync()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext == null)
            return null;

        return await httpContext.GetTokenAsync("access_token");
    }

    private class PermissionCheckResponse
    {
        public bool IsGranted { get; set; }
    }

    private class MultiplePermissionCheckResponse
    {
        public Dictionary<string, bool> Results { get; set; } = new();
    }
}
