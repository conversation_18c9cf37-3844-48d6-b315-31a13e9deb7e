using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Attachments.Jobs;
using Imip.HotelFrontOffice.Companies;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.Payments;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Reservations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Documents.Invoice;

/// <summary>
/// Service for invoice document operations
/// </summary>
public class InvoiceDocumentService : ApplicationService, IInvoiceDocumentService
{
    private readonly IRepository<Payment, Guid> _paymentRepository;
    private readonly IRepository<PaymentDetail, Guid> _paymentDetailRepository;
    private readonly IRepository<ReservationFoodAndBeverage, Guid> _reservationFoodAndBeverageRepository;
    private readonly IRepository<ReservationRoom, Guid> _reservationRoomRepository;
    private readonly IRepository<DocumentTemplate, Guid> _documentTemplateRepository;
    private readonly IDocumentTemplateRepository _documentTemplateCustomRepository;
    private readonly IRepository<Attachment, Guid> _attachmentRepository;
    private readonly IRepository<Company, Guid> _companyRepository;
    private readonly IBlobContainer _blobContainer;
    private readonly IConfiguration _configuration;
    private readonly IAttachmentAppService _attachmentAppService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ITemporaryZipFileTickerService _temporaryZipFileTickerService;
    private readonly IRepository<TemporaryZipFile, Guid> _temporaryZipFileRepository;
    private readonly WismaInvoiceGenerator _wismaInvoiceGenerator;
    private readonly ISyncfusionDocxToPdfService _syncfusionDocxToPdfService;

    public InvoiceDocumentService(
        IRepository<Payment, Guid> paymentRepository,
        IRepository<PaymentDetail, Guid> paymentDetailRepository,
        IRepository<DocumentTemplate, Guid> documentTemplateRepository,
        IDocumentTemplateRepository documentTemplateCustomRepository,
        IRepository<Attachment, Guid> attachmentRepository,
        IRepository<Company, Guid> companyRepository,
        IBlobContainerFactory blobContainerFactory,
        IConfiguration configuration,
        IAttachmentAppService attachmentAppService,
        IHttpClientFactory httpClientFactory,
        ITemporaryZipFileTickerService temporaryZipFileTickerService,
        IRepository<TemporaryZipFile, Guid> temporaryZipFileRepository,
        IRepository<ReservationFoodAndBeverage, Guid> reservationFoodAndBeverageRepository,
        IRepository<ReservationRoom, Guid> reservationRoomRepository,
        WismaInvoiceGenerator wismaInvoiceGenerator,
        ISyncfusionDocxToPdfService syncfusionDocxToPdfService)
    {
        _paymentRepository = paymentRepository;
        _paymentDetailRepository = paymentDetailRepository;
        _documentTemplateRepository = documentTemplateRepository;
        _documentTemplateCustomRepository = documentTemplateCustomRepository;
        _attachmentRepository = attachmentRepository;
        _companyRepository = companyRepository;
        _blobContainer = blobContainerFactory.Create("default");
        _configuration = configuration;
        _attachmentAppService = attachmentAppService;
        _httpClientFactory = httpClientFactory;
        _temporaryZipFileTickerService = temporaryZipFileTickerService;
        _temporaryZipFileRepository = temporaryZipFileRepository;
        _reservationFoodAndBeverageRepository = reservationFoodAndBeverageRepository;
        _reservationRoomRepository = reservationRoomRepository;
        _wismaInvoiceGenerator = wismaInvoiceGenerator;
        _syncfusionDocxToPdfService = syncfusionDocxToPdfService;
    }

    /// <summary>
    /// Generates an invoice document for a payment
    /// </summary>
    public async Task<FileDto> GenerateInvoiceAsync(InvoiceGenerationDto input)
    {
        // Get the payment with related entities
        var payment = await _paymentRepository.GetAsync(input.PaymentId);
        if (payment == null)
        {
            throw new UserFriendlyException(L["PaymentNotFound"]);
        }

        // Get the template
        DocumentTemplate? template;
        if (input.TemplateId.HasValue)
        {
            template = await _documentTemplateRepository.GetAsync(input.TemplateId.Value);
        }
        else
        {
            template = await _documentTemplateCustomRepository.GetDefaultTemplateAsync(DocumentType.Invoice);
            if (template == null)
            {
                throw new UserFriendlyException(L["NoDefaultTemplateFound", DocumentType.Invoice]);
            }
        }

        // Get the attachment (DOCX template)
        var attachment = await _attachmentRepository.GetAsync(template.AttachmentId);
        if (attachment == null)
        {
            throw new UserFriendlyException(L["TemplateAttachmentNotFound"]);
        }

        // Get the DOCX file from blob storage
        if (attachment.BlobName == null)
        {
            throw new UserFriendlyException(L["TemplateBlobNameNotFound"]);
        }

        var docxStream = await _blobContainer.GetAsync(attachment.BlobName);
        if (docxStream == null)
        {
            throw new UserFriendlyException(L["TemplateFileNotFound"]);
        }

        // Read the DOCX file into memory
        using var memoryStream = new MemoryStream();
        await docxStream.CopyToAsync(memoryStream);
        memoryStream.Position = 0;

        // Get the invoice data
        var invoiceData = await GetInvoiceTemplateDataAsync(input.PaymentId);

        // Process the template
        var processedDocxBytes = await ProcessTemplateAsync(memoryStream, invoiceData, input.IncludePaymentDetails, input.UseAdvancedTable);

        // Generate the filename
        var filename = input.CustomFilename ?? $"Invoice_{invoiceData.InvoiceNumber}_{DateTime.Now:yyyyMMdd_HHmmss}";

        // Return the file
        if (input.GeneratePdf)
        {
            // Convert to PDF using our Syncfusion-based conversion service
            return await _syncfusionDocxToPdfService.ConvertDocxToPdfAsync(processedDocxBytes, filename);
        }
        else
        {
            return new FileDto(
                $"{filename}.docx",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                processedDocxBytes
            );
        }
    }

    /// <summary>
    /// Gets the invoice template data for a payment
    /// </summary>
    public async Task<InvoiceTemplateDataDto> GetInvoiceTemplateDataAsync(Guid paymentId)
    {
        // Get the payment with related entities
        var paymentQuery = await _paymentRepository.WithDetailsAsync(
            p => p.Reservations!,
            p => p.PaymentDetails!,
            p => p.Status!,
            p => p.PaymentMethod!
        );

        var payment = paymentQuery.FirstOrDefault(p => p.Id == paymentId)
            ?? throw new UserFriendlyException(L["PaymentNotFound"]);

        // Get payment details with all necessary relationships
        var paymentDetailsQuery = await _paymentDetailRepository.WithDetailsAsync(
            pd => pd.ReservationDetails!,
            pd => pd.ReservationDetails.Room!,
            pd => pd.ReservationDetails.Room.RoomType!
        );

        var paymentDetails = paymentDetailsQuery.Where(pd => pd.PaymentId == paymentId).ToList();

        // Get company information
        Company? company = null;
        if (payment.Reservations?.CompanyId != null)
        {
            company = await _companyRepository.FindAsync(payment.Reservations.CompanyId.Value);
        }

        // Get room numbers from all reservation details
        var roomNumbers = paymentDetails
            .Where(pd => pd.ReservationDetails?.Room?.RoomNumber != null)
            .Select(pd => pd.ReservationDetails?.Room?.RoomNumber ?? string.Empty)
            .Where(rn => !string.IsNullOrEmpty(rn))
            .Distinct()
            .ToList();

        // Get check-in and check-out dates
        var checkInDate = paymentDetails
            .Where(pd => pd.ReservationDetails?.CheckInDate != null)
            .Select(pd => pd.ReservationDetails?.CheckInDate)
            .OrderBy(d => d)
            .FirstOrDefault();

        var checkOutDate = paymentDetails
            .Where(pd => pd.ReservationDetails?.CheckOutDate != null)
            .Select(pd => pd.ReservationDetails?.CheckOutDate)
            .OrderByDescending(d => d)
            .FirstOrDefault();

        // Create the invoice data
        var invoiceData = new InvoiceTemplateDataDto
        {
            InvoiceNumber = payment.PaymentCode ?? $"INV-{DateTime.Now:yyyyMMdd}-{paymentId.ToString().Substring(0, 8)}",
            InvoiceDate = DateTime.Now,
            PaymentCode = payment.PaymentCode ?? string.Empty,
            TransactionDate = payment.TransactionDate,
            ReservationCode = payment.Reservations?.ReservationCode ?? string.Empty,
            BookerName = payment.Reservations?.BookerName ?? string.Empty,
            // GuestName = payment.Reservations?.ReservationDetails?.Fullname ?? string.Empty,
            // GuestName = payment.ReservationDetails?.Guest?.Fullname ?? string.Empty,
            PaymentMethod = payment.PaymentMethod?.Name ?? string.Empty,
            PaymentStatus = payment.Status?.Name ?? string.Empty,
            TotalAmount = payment.TotalAmount ?? 0,
            PaidAmount = payment.PaidAmount ?? 0,
            VatRate = payment.VatRate,
            VatAmount = payment.VatAmount,
            GrantTotal = payment.GrantTotal ?? 0,
            CompanyName = company?.Name ?? _configuration["CompanyInfo:Name"] ?? "Hotel Front Office",
            SettlementCompany = GetSettlementCompany(payment, company?.Name ?? _configuration["CompanyInfo:Name"] ?? "Hotel Front Office"),
            CompanyAddress = company?.Address ?? _configuration["CompanyInfo:Address"] ?? string.Empty,
            CompanyPhone = _configuration["CompanyInfo:Phone"] ?? string.Empty,
            CompanyEmail = _configuration["CompanyInfo:Email"] ?? string.Empty,
            CompanyLogoUrl = _configuration["CompanyInfo:LogoUrl"] ?? string.Empty,
            // Use the aggregated room numbers and dates
            RoomNumber = string.Join(", ", roomNumbers),
            CheckInDate = checkInDate,
            CheckOutDate = checkOutDate
        };

        // Add payment details with enhanced descriptions
        foreach (var detail in paymentDetails)
        {
            // Get the source ID for the payment detail
            var sourceId = detail.SourceId ?? Guid.Empty;

            // Get the description based on the source type
            var description = await GetPaymentDetailDescriptionAsync(detail);

            // Get the transaction date based on the source type
            var transactionDate = await GetPaymentDetailTransactionDateAsync(detail);

            invoiceData.PaymentDetails.Add(new InvoicePaymentDetailDto
            {
                CheckInDate = detail.ReservationDetails?.CheckInDate,
                CheckOutDate = detail.ReservationDetails?.CheckOutDate,
                RoomNumber = detail.ReservationDetails?.Room?.RoomNumber ?? string.Empty,
                SourceType = detail.SourceType.HasValue ? detail.SourceType.Value.ToString() : string.Empty,
                SourceId = sourceId,
                Description = description,
                Amount = detail.Amount ?? 0,
                Qty = detail.Qty ?? 1,
                TransactionDate = transactionDate
            });
        }

        return invoiceData;
    }

    /// <summary>
    /// Gets the settlement company name based on VAT amount
    /// </summary>
    private string GetSettlementCompany(Payment payment, string companyName)
    {
        // Display settlement company only when payment method is "Company Invoice"
        if (payment.PaymentMethod?.Name == "Company Invoice")
        {
            return companyName;
        }

        // For all other payment methods, return empty string
        return string.Empty;
    }

    private async Task<string> GetPaymentDetailDescriptionAsync(PaymentDetail detail)
    {
        if (detail.SourceType == null)
        {
            return "Unknown payment detail";
        }
        switch (detail.SourceType.Value)
        {
            case PaymentSourceType.ReservationRoom:
                // Navigate through: PaymentDetail -> ReservationDetail -> Room -> RoomType
                var roomType = detail.ReservationDetails?.Room?.RoomType?.Alias;
                var roomNumber = detail.ReservationDetails?.Room?.RoomNumber;

                return roomType ?? "";

            case PaymentSourceType.ReservationRoomFoodAndBeverage:
                var queryReservationFoodAndBeverage = await _reservationFoodAndBeverageRepository
                    .WithDetailsAsync(rfb => rfb.FoodAndBeverage!);

                var reservationFoodAndBeverage = detail.SourceId.HasValue
                    ? queryReservationFoodAndBeverage.FirstOrDefault(
                        rfb => rfb.Id == detail.SourceId.Value &&
                                   detail.SourceType == PaymentSourceType.ReservationRoomFoodAndBeverage
                                   && rfb.ReservationDetailsId == detail.ReservationDetailsId
                        )
                    : null;

                var foodAndBeverageName = reservationFoodAndBeverage?.FoodAndBeverage?.Name;
                return foodAndBeverageName ?? "";

            case PaymentSourceType.ReservationRoomService:
                var queryReservationRoom = await _reservationRoomRepository
                                    .WithDetailsAsync(rfb => rfb.Services!);

                var reservationRoom = detail.SourceId.HasValue
                    ? queryReservationRoom.FirstOrDefault(
                        rfb => rfb.Id == detail.SourceId.Value &&
                                   detail.SourceType == PaymentSourceType.ReservationRoomService
                                   && rfb.ReservationDetailsId == detail.ReservationDetailsId
                        )
                    : null;
                var serviceName = reservationRoom?.Services?.Name;
                return serviceName ?? "";

            default:
                return "";
        }
    }

    private async Task<DateTime?> GetPaymentDetailTransactionDateAsync(PaymentDetail detail)
    {
        if (detail.SourceType == null)
        {
            return null;
        }

        switch (detail.SourceType.Value)
        {
            case PaymentSourceType.ReservationRoom:
                // Room charges don't have transaction dates, return null
                return null;

            case PaymentSourceType.ReservationRoomFoodAndBeverage:
                var queryReservationFoodAndBeverage = await _reservationFoodAndBeverageRepository
                    .GetQueryableAsync();

                var reservationFoodAndBeverage = detail.SourceId.HasValue
                    ? queryReservationFoodAndBeverage.FirstOrDefault(
                        rfb => rfb.Id == detail.SourceId.Value &&
                               rfb.ReservationDetailsId == detail.ReservationDetailsId
                        )
                    : null;

                return reservationFoodAndBeverage?.TransactionDate;

            case PaymentSourceType.ReservationRoomService:
                var queryReservationRoom = await _reservationRoomRepository
                    .GetQueryableAsync();

                var reservationRoom = detail.SourceId.HasValue
                    ? queryReservationRoom.FirstOrDefault(
                        rfb => rfb.Id == detail.SourceId.Value &&
                               rfb.ReservationDetailsId == detail.ReservationDetailsId
                        )
                    : null;

                return reservationRoom?.TransactionDate;

            default:
                return null;
        }
    }

    private async Task<byte[]> ProcessTemplateAsync(Stream templateStream, InvoiceTemplateDataDto data, bool includePaymentDetails, bool useAdvancedTable = false)
    {
        using var memoryStream = new MemoryStream();
        await templateStream.CopyToAsync(memoryStream);
        memoryStream.Position = 0;

        using (var wordDocument = WordprocessingDocument.Open(memoryStream, true))
        {
            // Replace placeholders in the document
            ReplaceTextPlaceholders(wordDocument, data);

            // Replace table placeholder with actual table if payment details are included
            if (includePaymentDetails && data.PaymentDetails.Count > 0)
            {
                ReplaceTablePlaceholder(wordDocument, data.PaymentDetails, useAdvancedTable, _configuration);
            }
        }

        return memoryStream.ToArray();
    }

    /// <summary>
    /// Converts DOCX content to PDF using Syncfusion DocIO
    /// </summary>
    /// <param name="docxBytes">The DOCX content as byte array</param>
    /// <param name="filename">The filename without extension</param>
    /// <returns>A FileDto containing the PDF content</returns>
    private async Task<FileDto> ConvertDocxToPdf(byte[] docxBytes, string filename)
    {
        try
        {
            // Use the Syncfusion-based conversion service
            return await _syncfusionDocxToPdfService.ConvertDocxToPdfAsync(docxBytes, filename);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error converting DOCX to PDF using Syncfusion");

            return new FileDto(
                $"{filename}.docx",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                docxBytes
            );
        }
    }

    /// <summary>
    /// Saves a PDF file to SFTP storage and creates an attachment record
    /// </summary>
    /// <param name="pdfBytes">The PDF content</param>
    /// <param name="fileName">The file name</param>
    /// <param name="tempFilePath">The temporary file path (not used, but kept for reference)</param>
    /// <returns>The created attachment</returns>
    private async Task<Attachment> SavePdfToSftpAsync(byte[] pdfBytes, string fileName, string _)
    {
        try
        {
            // Generate a unique blob name
            var blobName = $"invoices/{Guid.NewGuid():N}_{fileName}";

            // Save the file to blob storage
            using var memoryStream = new MemoryStream(pdfBytes);
            await _blobContainer.SaveAsync(blobName, memoryStream);

            // Create an attachment entity
            var attachment = new Attachment(
                GuidGenerator.Create(),
                fileName,
                "application/pdf",
                pdfBytes.Length,
                blobName,
                "Generated invoice PDF",
                null,
                "Invoice"
            );

            // Save the attachment metadata to the database
            await _attachmentRepository.InsertAsync(attachment);


            return attachment;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving PDF to SFTP storage");
            throw;
        }
    }

    /// <summary>
    /// Schedules cleanup of temporary files
    /// </summary>
    /// <param name="docxPath">The DOCX file path</param>
    /// <param name="pdfPath">The PDF file path</param>
    private async Task ScheduleTemporaryFileCleanupAsync(string docxPath, string pdfPath)
    {
        try
        {
            // Create a temporary zip file record for cleanup
            var tempFile = new TemporaryZipFile(
                GuidGenerator.Create(),
                string.Empty, // No blob name since we're just tracking local files
                DateTime.UtcNow.AddMinutes(5) // Delete after 5 minutes
            );

            // Store the file paths in the Description field
            tempFile.Description = $"{docxPath}|{pdfPath}";

            // Save the entity to the database
            await _temporaryZipFileRepository.InsertAsync(tempFile);

            // Schedule deletion using TickerQ
            await _temporaryZipFileTickerService.ScheduleDeleteAsync(
                tempFile.Id,
                tempFile.DeleteAfter
            );

        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to schedule cleanup of temporary files");
            // Don't throw the exception, as this is not critical
        }
    }

    private static void ReplaceTextPlaceholders(WordprocessingDocument wordDocument, InvoiceTemplateDataDto data)
    {
        var mainPart = wordDocument.MainDocumentPart;
        if (mainPart == null) return;

        var document = mainPart.Document;
        if (document == null) return;

        // Create a dictionary of placeholders and their values
        var placeholders = new Dictionary<string, string>
        {
            { "{{INVOICE_NO}}", data.InvoiceNumber },
            { "{{INVOICE_DATE}}", data.InvoiceDate.ToString("yyyy-MM-dd") },
            { "{{PAYMENT_CODE}}", data.PaymentCode },
            { "{{TRANSACTION_DATE}}", data.TransactionDate?.ToString("yyyy-MM-dd") ?? string.Empty },
            { "{{RESERVATION_CODE}}", data.ReservationCode },
            { "{{BOOKER_NAME}}", data.BookerName },
            { "{{GUEST_NAME}}", data.GuestName },
            { "{{PAYMENT_METHOD}}", data.PaymentMethod },
            { "{{PAYMENT_STATUS}}", data.PaymentStatus },
            { "{{TOTAL_AMOUNT}}", data.TotalAmount.ToString("N2") },
            { "{{PAID_AMOUNT}}", data.PaidAmount.ToString("N2") },
            { "{{COMPANY_NAME}}", data.CompanyName },
            { "{{COMPANY_ADDRESS}}", data.CompanyAddress },
            { "{{COMPANY_PHONE}}", data.CompanyPhone },
            { "{{COMPANY_EMAIL}}", data.CompanyEmail },
            { "{{CHECK_IN_DATE}}", data.CheckInDate?.ToString("yyyy-MM-dd") ?? string.Empty },
            { "{{CHECK_OUT_DATE}}", data.CheckOutDate?.ToString("yyyy-MM-dd") ?? string.Empty },
            { "{{ROOM_NUMBER}}", data.RoomNumber }
        };

        // Replace placeholders in the document
        foreach (var text in mainPart.Document.Descendants<Text>())
        {
            if (text.Text.Contains("{{"))
            {
                foreach (var placeholder in placeholders)
                {
                    if (text.Text.Contains(placeholder.Key))
                    {
                        text.Text = text.Text.Replace(placeholder.Key, placeholder.Value);
                    }
                }
            }
        }
    }

    private static void ReplaceTablePlaceholder(WordprocessingDocument wordDocument, List<InvoicePaymentDetailDto> paymentDetails, bool useAdvancedTable = false, IConfiguration? configuration = null)
    {
        // Create table data using the InvoiceTableGenerator
        // Choose between simple or advanced table based on the parameter
        var tableData = useAdvancedTable
            ? InvoiceTableGenerator.CreateAdvancedInvoiceTable(paymentDetails)
            : InvoiceTableGenerator.CreateSimpleInvoiceTable(paymentDetails);

        // Replace the table placeholder with font size 9
        TableGenerator.ReplaceTablePlaceholder(wordDocument, "{{TABLE}}", tableData, 9, configuration);
    }

    /// <summary>
    /// Generates a Wisma invoice document for a payment
    /// </summary>
    public async Task<FileDto> GenerateWismaInvoiceAsync(InvoiceGenerationDto input)
    {
        // Get the payment with related entities
        var payment = await _paymentRepository.GetAsync(input.PaymentId);
        if (payment == null)
        {
            throw new UserFriendlyException(L["PaymentNotFound"]);
        }

        // Get the template
        DocumentTemplate? template;
        if (input.TemplateId.HasValue)
        {
            template = await _documentTemplateRepository.GetAsync(input.TemplateId.Value);
        }
        else
        {
            template = await _documentTemplateCustomRepository.GetDefaultTemplateAsync(DocumentType.Invoice);
            if (template == null)
            {
                throw new UserFriendlyException(L["NoDefaultTemplateFound", DocumentType.Invoice]);
            }
        }

        // Get the attachment (DOCX template)
        var attachment = await _attachmentRepository.GetAsync(template.AttachmentId);
        if (attachment == null)
        {
            throw new UserFriendlyException(L["TemplateAttachmentNotFound"]);
        }

        // Get the DOCX file from blob storage
        if (attachment.BlobName == null)
        {
            throw new UserFriendlyException(L["TemplateBlobNameNotFound"]);
        }

        var docxStream = await _blobContainer.GetAsync(attachment.BlobName);
        if (docxStream == null)
        {
            throw new UserFriendlyException(L["TemplateFileNotFound"]);
        }

        // Read the DOCX file into memory
        using var memoryStream = new MemoryStream();
        await docxStream.CopyToAsync(memoryStream);
        memoryStream.Position = 0;

        // Get the invoice data
        var invoiceData = await GetInvoiceTemplateDataAsync(input.PaymentId);

        // Add the CreatedBy field which is needed for the Wisma template
        invoiceData.CreatedBy = CurrentUser.UserName;

        // Process the template using the enhanced Wisma invoice generator V2
        using (var wordDocument = WordprocessingDocument.Open(memoryStream, true))
        {
            _wismaInvoiceGenerator.GenerateCompleteInvoice(wordDocument, invoiceData);
        }

        // Generate the filename
        var filename = input.CustomFilename ?? $"Invoice_{invoiceData.InvoiceNumber}_{DateTime.Now:yyyyMMdd_HHmmss}";

        // Return the file
        if (input.GeneratePdf)
        {
            try
            {
                // Convert to PDF using our Syncfusion-based conversion service
                return await _syncfusionDocxToPdfService.ConvertDocxToPdfAsync(memoryStream.ToArray(), filename);
            }
            catch (OutOfMemoryException ex)
            {
                Logger.LogError(ex, "Out of memory during PDF conversion for Wisma invoice {Filename}. Returning DOCX instead.", filename);
                return new FileDto(
                    $"{filename}.docx",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    memoryStream.ToArray()
                );
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "PDF conversion failed for Wisma invoice {Filename}. Returning DOCX instead.", filename);
                return new FileDto(
                    $"{filename}.docx",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    memoryStream.ToArray()
                );
            }
        }
        else
        {
            return new FileDto(
                $"{filename}.docx",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                memoryStream.ToArray()
            );
        }
    }

    /// <summary>
    /// Generates a Wisma invoice document for a payment and saves it as an attachment
    /// </summary>
    public async Task<FileUploadResultDto> GenerateWismaInvoiceAsAttachmentAsync(InvoiceGenerationDto input)
    {
        // Generate the invoice file
        var fileDto = await GenerateWismaInvoiceAsync(input);

        // Get the payment to use as reference
        var payment = await _paymentRepository.GetAsync(input.PaymentId);

        // Create file upload DTO
        var fileUploadDto = new FileUploadDto
        {
            Description = $"Invoice for payment {payment.PaymentCode}",
            ReferenceId = payment.Id,
            ReferenceType = "Payment"
        };

        // Upload the file as an attachment
        var result = await _attachmentAppService.UploadFileAsync(
            fileUploadDto,
            fileDto.FileName,
            fileDto.ContentType,
            fileDto.Content);

        return result;
    }
}
