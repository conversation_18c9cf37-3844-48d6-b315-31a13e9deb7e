using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Reports;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.Reports;

[Route("api/report")]
[RemoteService]
public class ReportController : HotelFrontOfficeController
{
    private readonly IReportAppService _reportAppService;
    private readonly IRepository<Report> _repository;
    private readonly ILogger<ReportController> _logger;

    public ReportController(
        IReportAppService reportAppService,
        IRepository<Report> repository,
        ILogger<ReportController> logger)
    {
        _reportAppService = reportAppService;
        _repository = repository;
        _logger = logger;
    }

    [HttpGet]
    public async Task<PagedResultDto<ReportDto>> GetListAsync([FromQuery] PagedAndSortedResultRequestDto input)
    {
        return await _reportAppService.GetListAsync(input);
    }

    [HttpGet("active")]
    public async Task<List<ReportDto>> GetActiveReportsAsync()
    {
        return await _reportAppService.GetActiveReportsAsync();
    }

    [HttpGet("{id}")]
    public async Task<ReportDto> GetAsync(Guid id)
    {
        return await _reportAppService.GetAsync(id);
    }

    [HttpPost]
    public async Task<ReportDto> CreateAsync(CreateUpdateReportDto input)
    {
        return await _reportAppService.CreateAsync(input);
    }

    [HttpPut("{id}")]
    public async Task<ReportDto> UpdateAsync(Guid id, CreateUpdateReportDto input)
    {
        return await _reportAppService.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    public async Task DeleteAsync(Guid id)
    {
        await _reportAppService.DeleteAsync(id);
    }

    [HttpPost("preview")]
    public async Task<ReportPreviewDto> PreviewReportAsync(ReportExecutionDto input)
    {
        return await _reportAppService.PreviewReportAsync(input);
    }

    [HttpPost("export/csv")]
    public async Task<IActionResult> ExportToCsvAsync(ReportExecutionDto input)
    {
        var data = await _reportAppService.ExportReportToCsvAsync(input);
        var report = await _reportAppService.GetAsync(input.ReportId);

        return File(data, "text/csv", $"{report.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.csv");
    }

    [HttpPost("export/excel")]
    public async Task<IActionResult> ExportToExcelAsync(ReportExecutionDto input)
    {
        var data = await _reportAppService.ExportReportToExcelAsync(input);
        var report = await _reportAppService.GetAsync(input.ReportId);

        return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            $"{report.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
    }

    [HttpPost("export/excel/custom")]
    public async Task<IActionResult> ExportToExcelWithCustomHeaderAsync(ReportExecutionWithCustomHeaderDto input)
    {
        var executionDto = new ReportExecutionDto
        {
            ReportId = input.ReportId,
            Parameters = input.Parameters
        };

        byte[] data;
        if (input.CustomHeader != null)
        {
            data = await _reportAppService.ExportReportToExcelWithCustomHeaderAsync(executionDto, input.CustomHeader);
        }
        else
        {
            data = await _reportAppService.ExportReportToExcelAsync(executionDto);
        }

        var report = await _reportAppService.GetAsync(input.ReportId);

        return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            $"{report.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
    }

    [HttpGet("{reportId}/parameters")]
    public async Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId)
    {
        return await _reportAppService.GetReportParametersAsync(reportId);
    }

    [HttpGet("{reportId}/excel-header")]
    public async Task<ReportExcelHeaderDto> GetReportExcelHeaderConfigAsync(Guid reportId)
    {
        return await _reportAppService.GetReportExcelHeaderConfigAsync(reportId);
    }

    [HttpPut("{reportId}/excel-header")]
    public async Task UpdateReportExcelHeaderConfigAsync(Guid reportId, ReportExcelHeaderDto headerConfig)
    {
        await _reportAppService.UpdateReportExcelHeaderConfigAsync(reportId, headerConfig);
    }

    /// <summary>
    /// Get a paged list of reports with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of reports in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    [ProducesResponseType(typeof(PagedResultDto<ReportDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<ReportDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get total count before paging
            var totalCount = await query.CountAsync();

            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<Report>, List<ReportDto>>(items);

            // Return a standard ABP paged result
            return new PagedResultDto<ReportDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of reports: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve reports list",
                "Error.ReportsList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<Report>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with includes
        var query = await _repository.GetQueryableAsync();

        // Add includes for related entities if needed
        query = query.AsNoTracking();

        // Check if we need to include deeper relationships based on filter or sort fields
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Report>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Report>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided
            var sortDesc = parameters.Sorting.StartsWith('-');
            var sortField = sortDesc ? parameters.Sorting[1..] : parameters.Sorting;
            query = DynamicQueryBuilder<Report>.ApplySorting(query, sortField, sortDesc);
        }
        else
        {
            // Default sorting by name
            query = query.OrderBy(x => x.Name);
        }

        return query;
    }
}
