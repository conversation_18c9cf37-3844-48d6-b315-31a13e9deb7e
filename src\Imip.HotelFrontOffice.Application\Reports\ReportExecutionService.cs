using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Reports;

[RemoteService(false)]
public class ReportExecutionService : ApplicationService, IReportExecutionService
{
    private readonly IRepository<Report, Guid> _reportRepository;
    private readonly IDbContextProvider<HotelFrontOfficeDbContext> _dbContextProvider;

    public ReportExecutionService(
        IRepository<Report, Guid> reportRepository,
        IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
    {
        _reportRepository = reportRepository;
        _dbContextProvider = dbContextProvider;

        // Set EPPlus license context for non-commercial use
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    public async Task<ReportPreviewDto> ExecuteReportAsync(Guid reportId, Dictionary<string, object> parameters)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (!report.IsActive)
            throw new BusinessException("Report is not active");

        var dbContext = await _dbContextProvider.GetDbContextAsync();
        var connection = dbContext.Database.GetDbConnection();

        var processedQuery = ProcessQueryParameters(report.Query, parameters);

        // Check if connection is already open, if not open it
        bool wasConnectionClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasConnectionClosed)
            {
                await connection.OpenAsync();
            }

            using var command = connection.CreateCommand();
            command.CommandText = processedQuery;
            command.CommandTimeout = 300; // 5 minutes timeout

            // Assign the current transaction if one exists
            var currentTransaction = dbContext.Database.CurrentTransaction;
            if (currentTransaction != null)
            {
                command.Transaction = currentTransaction.GetDbTransaction();
            }

            // Set command type based on report type
            switch (report.QueryType)
            {
                case ReportQueryType.StoredProcedure:
                    command.CommandType = CommandType.StoredProcedure;
                    AddParametersToCommand(command, parameters);
                    break;
                case ReportQueryType.RawQuery:
                case ReportQueryType.View:
                    command.CommandType = CommandType.Text;
                    break;
            }

            using var reader = await command.ExecuteReaderAsync();

            var result = new ReportPreviewDto
            {
                ReportName = report.Name,
                ExecutedAt = DateTime.UtcNow
            };

            // Get column names
            for (int i = 0; i < reader.FieldCount; i++)
            {
                result.Columns.Add(reader.GetName(i));
            }

            // Read data
            while (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    row[reader.GetName(i)] = value;
                }
                result.Data.Add(row);
            }

            result.TotalRows = result.Data.Count;
            return result;
        }
        finally
        {
            // Only close the connection if we opened it
            if (wasConnectionClosed && connection.State == ConnectionState.Open)
            {
                await connection.CloseAsync();
            }
        }
    }

    public async Task<byte[]> ExportReportToCsvAsync(Guid reportId, Dictionary<string, object> parameters)
    {
        var reportData = await ExecuteReportAsync(reportId, parameters);

        var csv = new StringBuilder();

        // Add headers
        csv.AppendLine(string.Join(",", reportData.Columns.Select(EscapeCsvValue)));

        // Add data rows
        foreach (var row in reportData.Data)
        {
            var values = reportData.Columns.Select(col =>
                EscapeCsvValue(row.ContainsKey(col) ? row[col]?.ToString() ?? "" : ""));
            csv.AppendLine(string.Join(",", values));
        }

        return Encoding.UTF8.GetBytes(csv.ToString());
    }

    public async Task<byte[]> ExportReportToExcelAsync(Guid reportId, Dictionary<string, object> parameters)
    {
        var reportData = await ExecuteReportAsync(reportId, parameters);
        var report = await _reportRepository.GetAsync(reportId);

        // Check if report has custom Excel header configuration
        if (!string.IsNullOrEmpty(report.ExcelHeaderConfig))
        {
            try
            {
                var customHeader = JsonSerializer.Deserialize<ReportExcelHeaderDto>(report.ExcelHeaderConfig);
                return await CreateExcelFileWithCustomHeaderAsync(report, reportData, customHeader);
            }
            catch (JsonException)
            {
                // Fall back to default if JSON is invalid
                return await CreateExcelFileAsync(report, reportData);
            }
        }

        return await CreateExcelFileAsync(report, reportData);
    }

    public async Task<byte[]> ExportReportToExcelWithCustomHeaderAsync(Guid reportId, Dictionary<string, object> parameters, ReportExcelHeaderDto customHeader)
    {
        var reportData = await ExecuteReportAsync(reportId, parameters);
        var report = await _reportRepository.GetAsync(reportId);

        return await CreateExcelFileWithCustomHeaderAsync(report, reportData, customHeader);
    }

    public async Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (string.IsNullOrEmpty(report.Parameters))
            return new List<ReportParameterDto>();

        try
        {
            return JsonSerializer.Deserialize<List<ReportParameterDto>>(report.Parameters) ?? new List<ReportParameterDto>();
        }
        catch
        {
            return new List<ReportParameterDto>();
        }
    }

    public async Task<ReportExcelHeaderDto> GetReportExcelHeaderConfigAsync(Guid reportId)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (string.IsNullOrEmpty(report.ExcelHeaderConfig))
            return null;

        try
        {
            return JsonSerializer.Deserialize<ReportExcelHeaderDto>(report.ExcelHeaderConfig);
        }
        catch
        {
            return null;
        }
    }

    public async Task UpdateReportExcelHeaderConfigAsync(Guid reportId, ReportExcelHeaderDto headerConfig)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (headerConfig == null)
        {
            report.ExcelHeaderConfig = null;
        }
        else
        {
            report.ExcelHeaderConfig = JsonSerializer.Serialize(headerConfig, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });
        }

        await _reportRepository.UpdateAsync(report);
    }

    private string ProcessQueryParameters(string query, Dictionary<string, object> parameters)
    {
        if (parameters == null || !parameters.Any())
            return query;

        var processedQuery = query;

        foreach (var param in parameters)
        {
            var placeholder = $"{{{{{param.Key}}}}}";
            var value = param.Value?.ToString() ?? "";

            // Basic SQL injection protection - escape single quotes
            value = value.Replace("'", "''");

            processedQuery = processedQuery.Replace(placeholder, value);
        }

        return processedQuery;
    }

    private void AddParametersToCommand(IDbCommand command, Dictionary<string, object> parameters)
    {
        if (parameters == null) return;

        foreach (var param in parameters)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = $"@{param.Key}";
            parameter.Value = param.Value ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }
    }

    private async Task<byte[]> CreateExcelFileAsync(Report report, ReportPreviewDto reportData)
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add(report.Name);

        // Add default title
        worksheet.Cells[1, 1].Value = report.Name;
        worksheet.Cells[1, 1].Style.Font.Size = 16;
        worksheet.Cells[1, 1].Style.Font.Bold = true;
        worksheet.Cells[1, 1, 1, reportData.Columns.Count].Merge = true;
        worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

        // Add generated date
        worksheet.Cells[2, 1].Value = $"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
        worksheet.Cells[2, 1, 2, reportData.Columns.Count].Merge = true;

        // Add column headers starting from row 4
        int headerRow = 4;
        for (int i = 0; i < reportData.Columns.Count; i++)
        {
            worksheet.Cells[headerRow, i + 1].Value = reportData.Columns[i];
            worksheet.Cells[headerRow, i + 1].Style.Font.Bold = true;
            worksheet.Cells[headerRow, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[headerRow, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
            worksheet.Cells[headerRow, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
        }

        // Add data rows
        int dataStartRow = headerRow + 1;
        for (int rowIndex = 0; rowIndex < reportData.Data.Count; rowIndex++)
        {
            var row = reportData.Data[rowIndex];
            for (int colIndex = 0; colIndex < reportData.Columns.Count; colIndex++)
            {
                var columnName = reportData.Columns[colIndex];
                var cellValue = row.ContainsKey(columnName) ? row[columnName] : null;

                worksheet.Cells[dataStartRow + rowIndex, colIndex + 1].Value = cellValue;
                worksheet.Cells[dataStartRow + rowIndex, colIndex + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }
        }

        // Auto-fit columns
        worksheet.Cells.AutoFitColumns();

        return package.GetAsByteArray();
    }

    private async Task<byte[]> CreateExcelFileWithCustomHeaderAsync(Report report, ReportPreviewDto reportData, ReportExcelHeaderDto customHeader)
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add(report.Name);

        int currentRow = 1;

        // Add custom title
        if (!string.IsNullOrEmpty(customHeader.Title))
        {
            worksheet.Cells[currentRow, 1].Value = customHeader.Title;
            worksheet.Cells[currentRow, 1, currentRow, reportData.Columns.Count].Merge = true;

            if (customHeader.TitleStyle != null)
            {
                ApplyStyleToCell(worksheet.Cells[currentRow, 1], customHeader.TitleStyle);
            }
            else
            {
                worksheet.Cells[currentRow, 1].Style.Font.Size = 16;
                worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }
            currentRow++;
        }

        // Add subtitle
        if (!string.IsNullOrEmpty(customHeader.SubTitle))
        {
            worksheet.Cells[currentRow, 1].Value = customHeader.SubTitle;
            worksheet.Cells[currentRow, 1, currentRow, reportData.Columns.Count].Merge = true;

            if (customHeader.SubTitleStyle != null)
            {
                ApplyStyleToCell(worksheet.Cells[currentRow, 1], customHeader.SubTitleStyle);
            }
            else
            {
                worksheet.Cells[currentRow, 1].Style.Font.Size = 12;
                worksheet.Cells[currentRow, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }
            currentRow++;
        }

        // Add generated date
        if (customHeader.ShowGeneratedDate)
        {
            worksheet.Cells[currentRow, 1].Value = $"Generated: {DateTime.Now.ToString(customHeader.DateFormat)}";
            worksheet.Cells[currentRow, 1, currentRow, reportData.Columns.Count].Merge = true;
            worksheet.Cells[currentRow, 1].Style.Font.Size = 9;
            currentRow++;
        }

        // Add empty row
        currentRow++;

        // Add custom header rows with colspan/rowspan support
        if (customHeader.HeaderRows?.Any() == true)
        {
            int headerStartRow = currentRow;

            foreach (var headerRow in customHeader.HeaderRows)
            {
                int currentCol = 1;

                foreach (var cell in headerRow.Cells)
                {
                    var excelRange = worksheet.Cells[currentRow, currentCol];

                    // Set cell value
                    excelRange.Value = cell.Text;

                    // Apply colspan and rowspan
                    if (cell.ColSpan > 1 || cell.RowSpan > 1)
                    {
                        var mergeRange = worksheet.Cells[currentRow, currentCol,
                            currentRow + cell.RowSpan - 1, currentCol + cell.ColSpan - 1];
                        mergeRange.Merge = true;
                        excelRange = mergeRange;
                    }

                    // Apply custom style or default header style
                    var styleToApply = cell.Style ?? customHeader.HeaderStyle;
                    if (styleToApply != null)
                    {
                        ApplyStyleToCell(excelRange, styleToApply);
                    }
                    else
                    {
                        // Default header style
                        excelRange.Style.Font.Bold = true;
                        excelRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        excelRange.Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
                        excelRange.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                        excelRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        excelRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    }

                    currentCol += cell.ColSpan;
                }

                currentRow++;
            }
        }
        else
        {
            // Add default column headers
            for (int i = 0; i < reportData.Columns.Count; i++)
            {
                worksheet.Cells[currentRow, i + 1].Value = reportData.Columns[i];

                if (customHeader.HeaderStyle != null)
                {
                    ApplyStyleToCell(worksheet.Cells[currentRow, i + 1], customHeader.HeaderStyle);
                }
                else
                {
                    worksheet.Cells[currentRow, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[currentRow, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[currentRow, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                    worksheet.Cells[currentRow, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }
            }
            currentRow++;
        }

        // Add data rows
        int dataStartRow = currentRow;
        for (int rowIndex = 0; rowIndex < reportData.Data.Count; rowIndex++)
        {
            var row = reportData.Data[rowIndex];
            for (int colIndex = 0; colIndex < reportData.Columns.Count; colIndex++)
            {
                var columnName = reportData.Columns[colIndex];
                var cellValue = row.ContainsKey(columnName) ? row[columnName] : null;

                var cell = worksheet.Cells[dataStartRow + rowIndex, colIndex + 1];
                cell.Value = cellValue;

                // Apply data style
                if (customHeader.DataStyle != null)
                {
                    ApplyStyleToCell(cell, customHeader.DataStyle);
                }
                else
                {
                    cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }
            }
        }

        // Auto-fit columns
        worksheet.Cells.AutoFitColumns();

        return package.GetAsByteArray();
    }

    private void ApplyStyleToCell(ExcelRange cell, ReportExcelStyleDto style)
    {
        if (style == null) return;

        // Font settings
        if (!string.IsNullOrEmpty(style.FontName))
            cell.Style.Font.Name = style.FontName;

        if (style.FontSize > 0)
            cell.Style.Font.Size = style.FontSize;

        cell.Style.Font.Bold = style.Bold;

        // Colors
        if (!string.IsNullOrEmpty(style.BackgroundColor))
        {
            cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
            cell.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml(style.BackgroundColor));
        }

        if (!string.IsNullOrEmpty(style.FontColor))
        {
            cell.Style.Font.Color.SetColor(ColorTranslator.FromHtml(style.FontColor));
        }

        // Alignment
        cell.Style.HorizontalAlignment = style.HorizontalAlignment?.ToLower() switch
        {
            "center" => ExcelHorizontalAlignment.Center,
            "right" => ExcelHorizontalAlignment.Right,
            _ => ExcelHorizontalAlignment.Left
        };

        cell.Style.VerticalAlignment = style.VerticalAlignment?.ToLower() switch
        {
            "middle" => ExcelVerticalAlignment.Center,
            "bottom" => ExcelVerticalAlignment.Bottom,
            _ => ExcelVerticalAlignment.Top
        };

        // Text wrapping
        cell.Style.WrapText = style.WrapText;

        // Border
        if (style.Border)
        {
            cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
        }
    }

    private string EscapeCsvValue(string value)
    {
        if (string.IsNullOrEmpty(value))
            return "";

        // Check if the value contains special characters that require escaping
        bool needsEscaping = value.Contains(',') || value.Contains('"') || value.Contains('\n') || value.Contains('\r');

        if (needsEscaping)
        {
            // Escape double quotes by doubling them
            value = value.Replace("\"", "\"\"");

            // Wrap the entire value in double quotes
            return $"\"{value}\"";
        }

        return value;
    }
}