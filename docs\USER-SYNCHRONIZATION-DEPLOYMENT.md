# User Synchronization Deployment Configuration

This document describes the deployment configuration for the User Synchronization feature across different environments.

## Overview

The User Synchronization feature has been configured for deployment across:
- **Local Development** - Full logging and debugging enabled
- **Kubernetes Development** - Detailed logging for troubleshooting
- **Kubernetes Production** - Optimized for performance with minimal logging
- **GitLab CI/CD** - Automated deployment with environment-specific settings

## Configuration Files Updated

### 1. Application Configuration Files

#### `src/Imip.HotelFrontOffice.Web/appsettings.json` (Base Configuration)
```json
{
  "UserSynchronization": {
    "IsEnabled": true,
    "UpdateExistingUsers": true,
    "SynchronizeRoles": true,
    "SynchronizeClaims": true,
    "EnableLogging": true
  }
}
```

#### `src/Imip.HotelFrontOffice.Web/appsettings.Development.json`
```json
{
  "Logging": {
    "LogLevel": {
      "Imip.HotelFrontOffice.Users.UserSynchronizationService": "Debug",
      "Imip.HotelFrontOffice.Web.Middleware.UserSynchronizationMiddleware": "Debug"
    }
  },
  "UserSynchronization": {
    "IsEnabled": true,
    "UpdateExistingUsers": true,
    "SynchronizeRoles": true,
    "SynchronizeClaims": true,
    "EnableLogging": true
  }
}
```

#### `src/Imip.HotelFrontOffice.Web/appsettings.Production.json`
```json
{
  "Logging": {
    "LogLevel": {
      "Imip.HotelFrontOffice.Users.UserSynchronizationService": "Information",
      "Imip.HotelFrontOffice.Web.Middleware.UserSynchronizationMiddleware": "Warning"
    }
  },
  "UserSynchronization": {
    "IsEnabled": true,
    "UpdateExistingUsers": true,
    "SynchronizeRoles": true,
    "SynchronizeClaims": true,
    "EnableLogging": false
  }
}
```

### 2. Kubernetes Configuration Files

#### `k8s/dev/configmap.yaml` (Development Environment)
```yaml
data:
  # User Synchronization Configuration for Development
  UserSynchronization__IsEnabled: "${DEV_USER_SYNC_ENABLED}"
  UserSynchronization__UpdateExistingUsers: "${DEV_USER_SYNC_UPDATE_EXISTING}"
  UserSynchronization__SynchronizeRoles: "${DEV_USER_SYNC_ROLES}"
  UserSynchronization__SynchronizeClaims: "${DEV_USER_SYNC_CLAIMS}"
  UserSynchronization__EnableLogging: "${DEV_USER_SYNC_LOGGING}"
```

#### `k8s/prod/configmap.yaml` (Production Environment)
```yaml
data:
  # User Synchronization Configuration for Production
  UserSynchronization__IsEnabled: "${PROD_USER_SYNC_ENABLED}"
  UserSynchronization__UpdateExistingUsers: "${PROD_USER_SYNC_UPDATE_EXISTING}"
  UserSynchronization__SynchronizeRoles: "${PROD_USER_SYNC_ROLES}"
  UserSynchronization__SynchronizeClaims: "${PROD_USER_SYNC_CLAIMS}"
  UserSynchronization__EnableLogging: "${PROD_USER_SYNC_LOGGING}"
```

### 3. GitLab CI/CD Configuration

#### `.gitlab-ci.yml` (Pipeline Variables)
```yaml
variables:
  # User Synchronization Configuration
  DEV_USER_SYNC_ENABLED: "true"
  DEV_USER_SYNC_UPDATE_EXISTING: "true"
  DEV_USER_SYNC_ROLES: "true"
  DEV_USER_SYNC_CLAIMS: "true"
  DEV_USER_SYNC_LOGGING: "true"
  
  PROD_USER_SYNC_ENABLED: "true"
  PROD_USER_SYNC_UPDATE_EXISTING: "true"
  PROD_USER_SYNC_ROLES: "true"
  PROD_USER_SYNC_CLAIMS: "true"
  PROD_USER_SYNC_LOGGING: "false"
```

## Environment-Specific Settings

### Development Environment
- **Full Logging**: Debug level logging for troubleshooting
- **All Features Enabled**: Complete synchronization functionality
- **Update Existing Users**: Always update user data
- **Synchronize Everything**: Roles, claims, and user properties

### Production Environment
- **Minimal Logging**: Information level only, no debug logs
- **Performance Optimized**: Reduced logging overhead
- **All Features Enabled**: Complete synchronization functionality
- **Secure Configuration**: Appropriate for production workloads

### Staging Environment
- **Balanced Logging**: Information level with some debugging
- **Full Testing**: All features enabled for testing
- **Production-like**: Similar to production but with more logging

## Deployment Process

### 1. GitLab CI/CD Pipeline

The pipeline automatically:
1. **Exports Environment Variables**: Sets user synchronization variables
2. **Applies ConfigMaps**: Uses `envsubst` to substitute variables
3. **Deploys Applications**: Applies the configuration to Kubernetes

#### Development Deployment
```bash
# Export variables
export DEV_USER_SYNC_ENABLED="true"
export DEV_USER_SYNC_UPDATE_EXISTING="true"
export DEV_USER_SYNC_ROLES="true"
export DEV_USER_SYNC_CLAIMS="true"
export DEV_USER_SYNC_LOGGING="true"

# Apply ConfigMap
envsubst < k8s/dev/configmap.yaml | kubectl apply -f -
```

#### Production Deployment
```bash
# Export variables
export PROD_USER_SYNC_ENABLED="true"
export PROD_USER_SYNC_UPDATE_EXISTING="true"
export PROD_USER_SYNC_ROLES="true"
export PROD_USER_SYNC_CLAIMS="true"
export PROD_USER_SYNC_LOGGING="false"

# Apply ConfigMap
envsubst < k8s/prod/configmap.yaml | kubectl apply -f -
```

### 2. Manual Deployment

For manual deployments, ensure environment variables are set:

```bash
# Set environment variables
export DEV_USER_SYNC_ENABLED="true"
export DEV_USER_SYNC_UPDATE_EXISTING="true"
export DEV_USER_SYNC_ROLES="true"
export DEV_USER_SYNC_CLAIMS="true"
export DEV_USER_SYNC_LOGGING="true"

# Apply configuration
envsubst < k8s/dev/configmap.yaml | kubectl apply -f -
```

## Configuration Options

### UserSynchronization Settings

| Setting | Description | Development | Production |
|---------|-------------|-------------|------------|
| `IsEnabled` | Enable/disable user synchronization | `true` | `true` |
| `UpdateExistingUsers` | Update existing users during sync | `true` | `true` |
| `SynchronizeRoles` | Synchronize user roles | `true` | `true` |
| `SynchronizeClaims` | Synchronize user claims | `true` | `true` |
| `EnableLogging` | Enable detailed logging | `true` | `false` |

### Logging Levels

| Component | Development | Production |
|-----------|-------------|------------|
| `UserSynchronizationService` | `Debug` | `Information` |
| `UserSynchronizationMiddleware` | `Debug` | `Warning` |

## Monitoring and Troubleshooting

### Development Environment
- **Full Debug Logs**: All synchronization activities logged
- **Detailed Error Messages**: Complete stack traces and context
- **Performance Metrics**: Timing information for optimization

### Production Environment
- **Essential Logs Only**: Critical errors and important events
- **Performance Optimized**: Minimal logging overhead
- **Security Focused**: No sensitive data in logs

### Log Examples

#### Development Logs
```
[Debug] UserSynchronizationService: Synchronizing user 12345 (john.doe) from claims
[Debug] UserSynchronizationMiddleware: Extracted JWT token from Authorization header
[Information] UserSynchronizationService: Successfully synchronized user 12345 (john.doe) to internal database
```

#### Production Logs
```
[Information] UserSynchronizationService: Successfully synchronized user 12345 (john.doe) to internal database
[Warning] UserSynchronizationMiddleware: User synchronization failed for request /api/reservations
```

## Security Considerations

### Environment Variables
- **Secure Storage**: Variables stored in GitLab CI/CD variables (masked)
- **Environment Isolation**: Separate variables for dev/prod
- **Access Control**: Limited access to production variables

### Configuration Security
- **No Hardcoded Secrets**: All sensitive data in environment variables
- **Principle of Least Privilege**: Minimal permissions for each environment
- **Audit Trail**: All configuration changes tracked in Git

## Rollback Procedures

### Configuration Rollback
1. **Revert Git Changes**: Use Git to revert configuration changes
2. **Redeploy Pipeline**: Trigger GitLab CI/CD pipeline
3. **Verify Configuration**: Check ConfigMaps in Kubernetes

### Emergency Disable
```bash
# Disable user synchronization immediately
kubectl patch configmap imip-wisma-config -n imip-wisma-dev-new -p '{"data":{"UserSynchronization__IsEnabled":"false"}}'
kubectl patch configmap imip-wisma-config -n imip-wisma-prod -p '{"data":{"UserSynchronization__IsEnabled":"false"}}'

# Restart pods to pick up new configuration
kubectl rollout restart deployment/imip-wisma-web -n imip-wisma-dev-new
kubectl rollout restart deployment/imip-wisma-web -n imip-wisma-prod
```

## Validation

### Post-Deployment Checks
1. **ConfigMap Verification**: Check that ConfigMaps contain correct values
2. **Pod Logs**: Verify user synchronization is working
3. **Database Checks**: Confirm users are being synchronized
4. **API Testing**: Test authentication and user creation

### Health Checks
```bash
# Check ConfigMap
kubectl get configmap imip-wisma-config -n imip-wisma-dev-new -o yaml

# Check pod logs
kubectl logs -l app=imip-wisma-web -n imip-wisma-dev-new --tail=100

# Test API endpoint
curl -H "Authorization: Bearer <token>" https://api-wisma-dev.imip.co.id/api/app/user-synchronization/health
```
