using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Documents.Invoice;

using Imip.HotelFrontOffice.Documents.Invoice;
using Imip.HotelFrontOffice.FoodAndBeverages;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Services;

/// <summary>
/// Generator for IMIP Wisma invoice templates
/// </summary>
public class WismaInvoiceGenerator
{
    private readonly IRepository<ReservationFoodAndBeverage, Guid> _reservationFoodAndBeverageRepository;
    private readonly IRepository<ReservationRoom, Guid> _reservationRoomRepository;
    private readonly IRepository<FoodAndBeverage, Guid> _foodAndBeverageRepository;
    private readonly IRepository<Service, Guid> _serviceRepository;
    private readonly ILogger<WismaInvoiceGenerator> _logger;
    private readonly IConfiguration _configuration;

    public WismaInvoiceGenerator(
        IRepository<ReservationFoodAndBeverage, Guid> reservationFoodAndBeverageRepository,
        IRepository<ReservationRoom, Guid> reservationRoomRepository,
        IRepository<FoodAndBeverage, Guid> foodAndBeverageRepository,
        IRepository<Service, Guid> serviceRepository,
        ILogger<WismaInvoiceGenerator> logger,
        IConfiguration configuration)
    {
        _reservationFoodAndBeverageRepository = reservationFoodAndBeverageRepository;
        _reservationRoomRepository = reservationRoomRepository;
        _foodAndBeverageRepository = foodAndBeverageRepository;
        _serviceRepository = serviceRepository;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Creates a complete invoice document based on the IMIP Wisma template
    /// </summary>
    public TableGenerationDto CreateWismaInvoiceTable(InvoiceTemplateDataDto data)
    {
        // Create the main invoice table
        var headerTable = CreateHeaderTable(data);
        var detailsTable = CreateDetailsTable(data);
        var consumptionTable = CreateConsumptionTable(data);

        // For the invoice, we'll return the consumption table
        // The header and details tables will be inserted separately in the document
        return consumptionTable;
    }

    /// <summary>
    /// Creates the header table with company information and invoice number
    /// </summary>
    private static TableGenerationDto CreateHeaderTable(InvoiceTemplateDataDto data)
    {
        var tableData = new TableGenerationDto
        {
            Headers = [],
            Rows = []
        };

        // Title row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "青山绿水招待所",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center,
                    ColSpan = 3
                }
            ]
        });

        // Subtitle row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "TSINGSHAN WISMA IMIP",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center,
                    ColSpan = 3
                }
            ]
        });

        // Document type row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "客户消费确认表",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center,
                    ColSpan = 3
                }
            ]
        });

        // Document type English row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "Customer Consumption Confirmation",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center,
                    ColSpan = 3
                }
            ]
        });

        // Invoice number row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "NO:",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Right,
                    ColSpan = 2
                },
                new() {
                    Text = data.InvoiceNumber,
                    HorizontalAlignment = TableCellAlignment.Left
                }
            ]
        });

        return tableData;
    }

    /// <summary>
    /// Creates the details table with room, period, and company information
    /// </summary>
    private static TableGenerationDto CreateDetailsTable(InvoiceTemplateDataDto data)
    {
        var tableData = new TableGenerationDto
        {
            Headers = [],
            Rows = []
        };

        // Room number row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "房间号码(Room No)",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Left
                },
                new() {
                    Text = data.RoomNumber,
                    HorizontalAlignment = TableCellAlignment.Left
                }
            ]
        });

        // Consumption period row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "消费时间(Consumption period)",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Left
                },
                new() {
                    Text = $"{data.CheckInDate?.ToString("yyyy-MM-dd")} - {data.CheckOutDate?.ToString("yyyy-MM-dd")}",
                    HorizontalAlignment = TableCellAlignment.Left
                }
            ]
        });

        // Company name row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "客户单位全称(Full name of Client Company)",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Left
                },
                new() {
                    Text = data.CompanyName,
                    HorizontalAlignment = TableCellAlignment.Left
                }
            ]
        });

        // Amount section - header row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "消费金额\n(Amount)",
                    IsBold = true,
                    RowSpan = 2,
                    VerticalAlignment = TableCellAlignment.Center,
                    HorizontalAlignment = TableCellAlignment.Center
                },
                new() {
                    Text = "币种\n(Currency)",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center
                },
                new() {
                    Text = "小写\n(Lowercase)",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center
                },
                new() {
                    Text = data.TotalAmount.ToString("N2"),
                    HorizontalAlignment = TableCellAlignment.Right
                }
            ]
        });

        // Amount section - second row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                // First cell is merged with the cell above
                new() {
                    Text = "印尼盾\n(Rp)",
                    HorizontalAlignment = TableCellAlignment.Center
                },
                new() {
                    Text = "大写\n(Uppercase)",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center
                },
                new() {
                    Text = ConvertToWords(data.TotalAmount),
                    HorizontalAlignment = TableCellAlignment.Left
                }
            ]
        });

        // Payment method row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "结算方式\n(Payment)",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center
                },
                new() {
                    Text = data.PaymentMethod,
                    HorizontalAlignment = TableCellAlignment.Left
                },
                new() {
                    Text = "结算公司\nSettlement company",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center
                },
                new() {
                    Text = data.CompanyName,
                    HorizontalAlignment = TableCellAlignment.Left
                }
            ]
        });

        // Application department and applicant row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = $"申请单位: {data.CompanyName}\n(Application Dept)",
                    HorizontalAlignment = TableCellAlignment.Left,
                    ColSpan = 2
                },
                new() {
                    Text = $"申请人: {data.BookerName}\n(Applicant)",
                    HorizontalAlignment = TableCellAlignment.Left,
                    ColSpan = 2
                }
            ]
        });

        // Preparer, consumer, and customer row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = $"开单人: {data.CreatedBy}\n(Preparer)",
                    HorizontalAlignment = TableCellAlignment.Left
                },
                new() {
                    Text = "消费人:\n(Consumer)",
                    HorizontalAlignment = TableCellAlignment.Left
                },
                new() {
                    Text = "签单人确认:\n(Customer)",
                    HorizontalAlignment = TableCellAlignment.Left,
                    ColSpan = 2
                }
            ]
        });

        // Print date row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "打印日期(Printed Date):",
                    HorizontalAlignment = TableCellAlignment.Left
                },
                new() {
                    Text = DateTime.Now.ToString("yyyy-MM-dd"),
                    HorizontalAlignment = TableCellAlignment.Left,
                    ColSpan = 3
                }
            ]
        });

        return tableData;
    }

    /// <summary>
    /// Creates the consumption details table with room charges, meal expenses, and other expenses
    /// </summary>
    private TableGenerationDto CreateConsumptionTable(InvoiceTemplateDataDto data)
    {
        // Process payment details by type
        decimal roomTotal = 0;
        decimal mealTotal = 0;
        decimal otherTotal = 0;

        // Ensure we have payment details
        if (data.PaymentDetails == null)
        {
            data.PaymentDetails = new List<InvoicePaymentDetailDto>();
        }

        // Get room details
        var roomDetails = data.PaymentDetails
            .Where(d => d.SourceType == PaymentSourceType.ReservationRoom.ToString())
            .ToList();

        // Get meal details
        var mealDetails = data.PaymentDetails
            .Where(d => d.SourceType == PaymentSourceType.ReservationRoomFoodAndBeverage.ToString())
            .ToList();

        // Get other details
        var otherDetails = data.PaymentDetails
            .Where(d => d.SourceType == PaymentSourceType.ReservationRoomService.ToString())
            .ToList();

        // Ensure we have data for each section by populating with default values if needed
        // EnsureDataForExample(roomDetails, mealDetails, otherDetails);

        // Calculate totals from the database values
        roomTotal = roomDetails.Sum(d => d.Amount);
        mealTotal = mealDetails.Sum(d => d.Amount);
        otherTotal = otherDetails.Sum(d => d.Amount);

        // Use the AlignedTableGenerator to create a table with aligned rows
        // Check if we need to add tax calculation rows
        // Tax calculation should only appear when:
        // 1. Payment method name ≠ "Company Invoice" (case-sensitive exact match)
        // 2. VatAmount > 0 AND VatRate > 0
        bool hasTax = data.VatAmount.HasValue && data.VatAmount.Value > 0 &&
                      data.VatRate.HasValue && data.VatRate.Value > 0 &&
                      !string.Equals(data.PaymentMethod, "Company Invoice", StringComparison.Ordinal);

        return CreateAlignedConsumptionTableWithTax(
            roomDetails,
            mealDetails,
            otherDetails,
            roomTotal,
            mealTotal,
            otherTotal,
            data,
            hasTax,
            FormatCurrency);
    }

    /// <summary>
    /// Creates the consumption details table with room charges, meal expenses, other expenses, and optional tax calculation
    /// </summary>
    private TableGenerationDto CreateAlignedConsumptionTableWithTax(
        List<InvoicePaymentDetailDto> roomDetails,
        List<InvoicePaymentDetailDto> mealDetails,
        List<InvoicePaymentDetailDto> otherDetails,
        decimal roomTotal,
        decimal mealTotal,
        decimal otherTotal,
        InvoiceTemplateDataDto data,
        bool hasTax,
        Func<decimal, string> formatCurrency)
    {
        // Start with the standard table from AlignedTableGenerator
        var tableData = AlignedTableGenerator.CreateAlignedConsumptionTable(
            roomDetails,
            mealDetails,
            otherDetails,
            roomTotal,
            mealTotal,
            otherTotal,
            formatCurrency);

        // If we have tax, add the tax calculation rows
        if (hasTax)
        {
            // Remove the existing grand total row (last row)
            if (tableData.Rows.Count > 0)
            {
                var lastRow = tableData.Rows[tableData.Rows.Count - 1];
                if (lastRow.Cells.Any(c => c.Text.Contains("总计") || c.Text.Contains("Total")))
                {
                    tableData.Rows.RemoveAt(tableData.Rows.Count - 1);
                }
            }

            // Add combined subtotal row (合计)
            decimal subtotal = roomTotal + mealTotal + otherTotal;
            tableData.Rows.Add(new TableRowDto
            {
                Cells =
                [
                    new() {
                        Text = "合计",
                        IsBold = true,
                        HorizontalAlignment = TableCellAlignment.Right,
                        ColSpan = 5,
                        WidthPercentage = 38.5
                    },
                    new() {
                        Text = formatCurrency(subtotal),
                        IsBold = true,
                        HorizontalAlignment = TableCellAlignment.Right,
                        ColSpan = 8,
                        WidthPercentage = 61.5
                    }
                ]
            });

            // Add tax calculation row (税11%: or similar based on VatRate)
            string taxLabel = $"税{data.VatRate?.ToString("0")}%：";
            tableData.Rows.Add(new TableRowDto
            {
                Cells =
                [
                    new() {
                        Text = taxLabel,
                        IsBold = true,
                        HorizontalAlignment = TableCellAlignment.Right,
                        ColSpan = 5,
                        WidthPercentage = 38.5
                    },
                    new() {
                        Text = formatCurrency(data.VatAmount ?? 0),
                        IsBold = true,
                        HorizontalAlignment = TableCellAlignment.Right,
                        ColSpan = 8,
                        WidthPercentage = 61.5
                    }
                ]
            });

            // Add grand total row (总计（Total）)
            tableData.Rows.Add(new TableRowDto
            {
                Cells =
                [
                    new() {
                        Text = "总计（Total）：",
                        IsBold = true,
                        HorizontalAlignment = TableCellAlignment.Right,
                        ColSpan = 5,
                        WidthPercentage = 38.5
                    },
                    new() {
                        Text = "Rp " + formatCurrency(data.GrantTotal != 0 ? data.GrantTotal : subtotal),
                        IsBold = true,
                        HorizontalAlignment = TableCellAlignment.Right,
                        ColSpan = 8,
                        WidthPercentage = 61.5
                    }
                ]
            });
        }

        return tableData;
    }

    /// <summary>
    /// Converts a decimal amount to words (for the uppercase amount)
    /// </summary>
    private static string ConvertToWords(decimal amount)
    {
        if (amount == 0)
            return "Zero Indonesian rupiahs";

        // Format with 2 decimal places
        string amountStr = amount.ToString("F2", CultureInfo.InvariantCulture);
        string[] parts = amountStr.Split('.');

        // Convert the whole number part
        long wholePart = long.Parse(parts[0]);
        string result = ConvertWholeNumberToWords(wholePart);

        // Handle decimal part (cents)
        string decimalPart = parts[1];
        if (decimalPart != "00")
        {
            int cents = int.Parse(decimalPart);
            result += " and " + ConvertWholeNumberToWords(cents) + " cents";
        }

        // Add currency designation
        result += " Indonesian rupiahs";

        return result.Trim();
    }

    /// <summary>
    /// Converts a whole number to words without currency designation
    /// </summary>
    private static string ConvertWholeNumberToWords(long number)
    {
        if (number == 0)
            return "zero";

        string[] units = { "", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine",
                          "ten", "eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen",
                          "seventeen", "eighteen", "nineteen" };

        string[] tens = { "", "", "twenty", "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety" };

        string result = "";

        // Handle billions
        if (number >= 1000000000)
        {
            long billions = number / 1000000000;
            result += ConvertHundreds(billions, units, tens) + " billion ";
            number %= 1000000000;
        }

        // Handle millions
        if (number >= 1000000)
        {
            long millions = number / 1000000;
            result += ConvertHundreds(millions, units, tens) + " million ";
            number %= 1000000;
        }

        // Handle thousands
        if (number >= 1000)
        {
            long thousands = number / 1000;
            result += ConvertHundreds(thousands, units, tens) + " thousand ";
            number %= 1000;
        }

        // Handle remaining hundreds, tens, and units
        if (number > 0)
        {
            result += ConvertHundreds(number, units, tens);
        }

        return result.Trim();
    }

    /// <summary>
    /// Converts a number less than 1000 to words
    /// </summary>
    private static string ConvertHundreds(long number, string[] units, string[] tens)
    {
        string result = "";

        // Handle hundreds
        if (number >= 100)
        {
            result += units[number / 100] + " hundred ";
            number %= 100;
        }

        // Handle tens and units
        if (number > 0)
        {
            if (number < 20)
            {
                result += units[number];
            }
            else
            {
                result += tens[number / 10];
                if (number % 10 > 0)
                {
                    result += " " + units[number % 10];
                }
            }
        }

        return result.Trim();
    }

    /// <summary>
    /// Generates a complete IMIP Wisma invoice document
    /// </summary>
    public void GenerateCompleteInvoice(WordprocessingDocument wordDocument, InvoiceTemplateDataDto data)
    {
        var mainPart = wordDocument.MainDocumentPart;
        if (mainPart == null) return;

        var document = mainPart.Document;
        if (document == null) return;

        // Find all placeholders in the document and replace them
        ReplaceTextPlaceholders(wordDocument, data);

        // Find the table placeholder and replace it with our tables
        var tablePlaceholderParagraph = document.Descendants<Paragraph>()
            .FirstOrDefault(p => p.InnerText.Contains("{{TABLE}}"));

        if (tablePlaceholderParagraph != null)
        {
            var parent = tablePlaceholderParagraph.Parent;
            if (parent != null)
            {
                // Create the consumption table with a custom title
                var consumptionTable = CreateConsumptionTable(data);

                // Remove the title row from the table to avoid duplication
                // The title is already in the template
                if (consumptionTable.Rows.Count > 0 &&
                    consumptionTable.Rows[0].Cells.Count > 0 &&
                    consumptionTable.Rows[0].Cells[0].Text.Contains("附(Annex)"))
                {
                    // Remove the title row
                    consumptionTable.Rows.RemoveAt(0);
                }

                var table = TableGenerator.CreateTable(wordDocument, consumptionTable, 9, _configuration);

                // Insert the table and remove the placeholder
                parent.InsertAfter(table, tablePlaceholderParagraph);
                tablePlaceholderParagraph.Remove();
            }
        }
    }

    /// <summary>
    /// Replaces text placeholders in the document with actual values
    /// </summary>
    private void ReplaceTextPlaceholders(WordprocessingDocument wordDocument, InvoiceTemplateDataDto data)
    {
        var mainPart = wordDocument.MainDocumentPart;
        if (mainPart == null) return;

        var document = mainPart.Document;
        if (document == null) return;

        // Create a dictionary of placeholders and their values
        var placeholders = new Dictionary<string, string>
        {
            { "{{PAYMENT_NUMBER}}", data.InvoiceNumber },
            { "{{ROOM_NO}}", data.RoomNumber },
            { "{{CHECK_IN}}", data.CheckInDate?.ToString("yyyy-MM-dd") ?? string.Empty },
            { "{{CHECK_OUT}}", data.CheckOutDate?.ToString("yyyy-MM-dd") ?? string.Empty },
            { "{{COMPANY_NAME}}", data.CompanyName },
            { "{{AMOUNT}}", "Rp " + data.GrantTotal.ToString("N2") },
            { "{{AMOUNT_TEXT}}", ConvertToWords(data.GrantTotal) },
            { "{{PAYMENT_METHOD}}", data.PaymentMethod },
            { "{{BOOKER_NAME}}", data.BookerName },
            { "{{CREATED_BY}}", data.CreatedBy ?? "System" },
            { "{{PRINT_DATE}}", DateTime.Now.ToString("yyyy-MM-dd HH:mm") },
            { "{{GRANT_TOTAL}}", "Rp " + (data.GrantTotal.ToString("N2") ?? data.GrantTotal.ToString("N2")) },
            { "{{PAYMENT_CODE}}", data.PaymentCode },
            { "{{RESERVATION_CODE}}", data.ReservationCode },
            { "{{TRANSACTION_DATE}}", data.TransactionDate?.ToString("yyyy-MM-dd") ?? "" },
            { "{{PAYMENT_STATUS}}", data.PaymentStatus },
            { "{{VAT_RATE}}", data.VatRate?.ToString("N2") ?? "0" },
            { "{{VAT_AMOUNT}}", FormatCurrency(data.VatAmount ?? 0) },
            { "{{PAID_AMOUNT}}", FormatCurrency(data.PaidAmount) },
            { "{{TOTAL_AMOUNT}}", FormatCurrency(data.GrantTotal) },
            { "{{GRAND_TOTAL}}", FormatCurrency(data.GrantTotal) },
            { "{{SETTLEMENT_COMPANY}}", data.SettlementCompany },
            // Add explicit mappings for table cells
            { "{{RM_TY}}", "" },
            { "{{RM_PR}}", "" },
            { "{{RM_QT}}", "" },
            { "{{RM_NI}}", "" },
            { "{{RM_AM}}", "" },
            { "{{RM_TO}}", "" },
            { "{{ML_TY}}", "" },
            { "{{ML_QT}}", "" },
            { "{{ML_PR}}", "" },
            { "{{ML_AM}}", "" },
            { "{{ML_TO}}", "" },
            { "{{OT_TY}}", "" },
            { "{{OT_QT}}", "" },
            { "{{OT_PR}}", "" },
            { "{{OT_AM}}", "" },
            { "{{OT_TO}}", "" }
        };

        // Add placeholders for room charges
        decimal roomTotal = 0;
        var roomDetails = data.PaymentDetails.Where(d =>
            d.SourceType == PaymentSourceType.ReservationRoom.ToString())
            .ToList();

        // Calculate the number of nights if check-in and check-out dates are available
        int nights = 1;
        if (data.CheckInDate.HasValue && data.CheckOutDate.HasValue)
        {
            nights = (int)(data.CheckOutDate.Value - data.CheckInDate.Value).TotalDays;
            if (nights <= 0) nights = 1; // Ensure at least 1 night
        }

        if (roomDetails.Count > 0)
        {
            // Handle multiple room details
            for (int i = 0; i < roomDetails.Count; i++)
            {
                var detail = roomDetails[i];
                string suffix = i == 0 ? "" : $"_{i + 1}"; // Empty suffix for first item, _2, _3, etc. for others

                // Extract room type from the description
                // The description should now contain the room type name from the relationship path:
                // PaymentDetail -> ReservationDetail -> Room -> RoomType
                string roomType = detail.Description;

                // If description is empty, use room number as fallback
                if (string.IsNullOrEmpty(roomType))
                {
                    roomType = !string.IsNullOrEmpty(detail.RoomNumber)
                        ? $"Room {detail.RoomNumber}"
                        : $"Room {data.RoomNumber}";
                }

                // Calculate price per night (total amount divided by nights)
                decimal qty = detail.Qty > 0 ? detail.Qty : 1;
                decimal pricePerUnit = detail.Amount / qty;

                // Add placeholders with unique keys for this row
                placeholders[$"{{{{RM_TY{suffix}}}}}"] = roomType;
                placeholders[$"{{{{RM_PR{suffix}}}}}"] = pricePerUnit.ToString("N2");
                placeholders[$"{{{{RM_QT{suffix}}}}}"] = qty.ToString("N0");
                placeholders[$"{{{{RM_NI{suffix}}}}}"] = nights.ToString();
                placeholders[$"{{{{RM_AM{suffix}}}}}"] = detail.Amount.ToString("N2");

                roomTotal += detail.Amount;
            }

            // Add total for all room charges
            placeholders["{{RM_TO}}"] = roomTotal.ToString("N2");

            // Add empty placeholders for unused rows (up to a reasonable maximum, e.g., 5)
            for (int i = roomDetails.Count; i < 5; i++)
            {
                string suffix = $"_{i + 1}";
                placeholders[$"{{{{RM_TY{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{RM_PR{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{RM_QT{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{RM_NI{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{RM_AM{suffix}}}}}"] = string.Empty;
            }
        }
        else
        {
            // No room details, add empty placeholders
            placeholders["{{RM_TY}}"] = string.Empty;
            placeholders["{{RM_PR}}"] = string.Empty;
            placeholders["{{RM_QT}}"] = string.Empty;
            placeholders["{{RM_NI}}"] = string.Empty;
            placeholders["{{RM_AM}}"] = string.Empty;
            placeholders["{{RM_TO}}"] = "0.00";

            // Add empty placeholders for additional rows
            for (int i = 1; i < 5; i++)
            {
                string suffix = $"_{i + 1}";
                placeholders[$"{{{{RM_TY{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{RM_PR{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{RM_QT{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{RM_NI{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{RM_AM{suffix}}}}}"] = string.Empty;
            }
        }

        // Add placeholders for meal expenses
        decimal mealTotal = 0;
        var mealDetails = data.PaymentDetails.Where(d =>
            d.SourceType == PaymentSourceType.ReservationRoomFoodAndBeverage.ToString())
            .ToList();

        if (mealDetails.Count > 0)
        {
            // Handle multiple meal details
            for (int i = 0; i < mealDetails.Count; i++)
            {
                var detail = mealDetails[i];
                string suffix = i == 0 ? "" : $"_{i + 1}"; // Empty suffix for first item, _2, _3, etc. for others

                // Extract food and beverage name from the description
                // For ReservationRoomFoodAndBeverage, we want to get the name from the FoodAndBeverage entity
                string foodAndBeverageName = GetActualName(detail, "ML");

                // If we got a generic name, try to use the description if it's meaningful
                if (foodAndBeverageName == "Food & Beverage" &&
                    !string.IsNullOrEmpty(detail.Description) &&
                    !detail.Description.Contains(detail.SourceId.ToString(), StringComparison.OrdinalIgnoreCase))
                {
                    foodAndBeverageName = detail.Description;
                }

                // If we still have a generic name, try one more approach
                if (foodAndBeverageName == "Food & Beverage" && !Guid.Empty.Equals(detail.SourceId))
                {
                    try
                    {
                        // Try to get the food and beverage directly
                        var query = _reservationFoodAndBeverageRepository
                            .WithDetailsAsync(rfb => rfb.FoodAndBeverage).GetAwaiter().GetResult();

                        if (query != null)
                        {
                            var reservationFoodAndBeverage = query.FirstOrDefault(rfb => rfb.Id == detail.SourceId);

                            if (reservationFoodAndBeverage?.FoodAndBeverage != null)
                            {
                                foodAndBeverageName = reservationFoodAndBeverage.FoodAndBeverage.Name;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error getting food and beverage name");
                    }
                }

                // Use Qty if available, otherwise default to 1
                decimal qty = detail.Qty > 0 ? detail.Qty : 1;
                decimal pricePerUnit = detail.Amount / qty;

                // Add placeholders with unique keys for this row
                placeholders[$"{{{{ML_TY{suffix}}}}}"] = foodAndBeverageName;
                placeholders[$"{{{{ML_PR{suffix}}}}}"] = pricePerUnit.ToString("N2");
                placeholders[$"{{{{ML_QT{suffix}}}}}"] = qty.ToString("N0");
                placeholders[$"{{{{ML_AM{suffix}}}}}"] = detail.Amount.ToString("N2");

                mealTotal += detail.Amount;
            }

            // Add total for all meal expenses
            placeholders["{{ML_TO}}"] = mealTotal.ToString("N2");

            // Add empty placeholders for unused rows (up to a reasonable maximum, e.g., 5)
            for (int i = mealDetails.Count; i < 5; i++)
            {
                string suffix = $"_{i + 1}";
                placeholders[$"{{{{ML_TY{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{ML_PR{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{ML_QT{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{ML_AM{suffix}}}}}"] = string.Empty;
            }
        }
        else
        {
            // No meal details, add empty placeholders
            placeholders["{{ML_TY}}"] = string.Empty;
            placeholders["{{ML_PR}}"] = string.Empty;
            placeholders["{{ML_QT}}"] = string.Empty;
            placeholders["{{ML_AM}}"] = string.Empty;
            placeholders["{{ML_TO}}"] = "0.00";

            // Add empty placeholders for additional rows
            for (int i = 1; i < 5; i++)
            {
                string suffix = $"_{i + 1}";
                placeholders[$"{{{{ML_TY{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{ML_PR{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{ML_QT{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{ML_AM{suffix}}}}}"] = string.Empty;
            }
        }

        // Add placeholders for other expenses (services)
        decimal otherTotal = 0;
        var otherDetails = data.PaymentDetails.Where(d =>
            d.SourceType == PaymentSourceType.ReservationRoomService.ToString())
            .ToList();

        // _logger.LogInformation("Processing other expenses: {Count}", otherDetails.Count);

        if (otherDetails.Count > 0)
        {
            // Handle multiple other expense details
            for (int i = 0; i < otherDetails.Count; i++)
            {
                var detail = otherDetails[i];
                string suffix = i == 0 ? "" : $"_{i + 1}"; // Empty suffix for first item, _2, _3, etc. for others

                // Extract service name from the description
                // For ReservationRoomService, we want to get the name from the Service entity
                string serviceName = GetActualName(detail, "OT");

                // If the GetActualName method returned a generic name, try to use the description
                if (serviceName == "Room Service" && !string.IsNullOrEmpty(detail.Description) &&
                    !detail.Description.Contains(detail.SourceId.ToString()) &&
                    !detail.Description.Contains("Room Service -"))
                {
                    serviceName = detail.Description;
                }

                // Use Qty if available, otherwise default to 1
                decimal qty = detail.Qty > 0 ? detail.Qty : 1;
                decimal pricePerUnit = detail.Amount / qty;

                // Add placeholders with unique keys for this row
                placeholders[$"{{{{OT_TY{suffix}}}}}"] = serviceName;
                placeholders[$"{{{{OT_PR{suffix}}}}}"] = pricePerUnit.ToString("N2");
                placeholders[$"{{{{OT_QT{suffix}}}}}"] = qty.ToString("N0");
                placeholders[$"{{{{OT_AM{suffix}}}}}"] = detail.Amount.ToString("N2");

                otherTotal += detail.Amount;
            }

            // Add total for all other expenses
            placeholders["{{OT_TO}}"] = otherTotal.ToString("N2");

            // Add empty placeholders for unused rows (up to a reasonable maximum, e.g., 5)
            for (int i = otherDetails.Count; i < 5; i++)
            {
                string suffix = $"_{i + 1}";
                placeholders[$"{{{{OT_TY{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{OT_PR{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{OT_QT{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{OT_AM{suffix}}}}}"] = string.Empty;
            }
        }
        else
        {
            // No other expense details, add empty placeholders
            placeholders["{{OT_TY}}"] = string.Empty;
            placeholders["{{OT_PR}}"] = string.Empty;
            placeholders["{{OT_QT}}"] = string.Empty;
            placeholders["{{OT_AM}}"] = string.Empty;
            placeholders["{{OT_TO}}"] = "0.00";

            // Add empty placeholders for additional rows
            for (int i = 1; i < 5; i++)
            {
                string suffix = $"_{i + 1}";
                placeholders[$"{{{{OT_TY{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{OT_PR{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{OT_QT{suffix}}}}}"] = string.Empty;
                placeholders[$"{{{{OT_AM{suffix}}}}}"] = string.Empty;
            }
        }

        // Replace placeholders in the document using a more robust approach
        ReplaceTextInDocument(mainPart.Document, placeholders);

        // Specifically handle table cells which might have special formatting
        ReplaceTextInTableCells(mainPart.Document, placeholders);

        // Handle the case where the template might have multiple rows for each category
        HandleMultipleRowsInTables(mainPart.Document, data);
    }

    /// <summary>
    /// Replaces text in the document, handling cases where placeholders might be split across multiple Text elements
    /// </summary>
    private static void ReplaceTextInDocument(Document document, Dictionary<string, string> placeholders)
    {
        try
        {
            // First, handle simple cases where the placeholder is entirely within a single Text element
            foreach (var text in document.Descendants<Text>())
            {
                if (text.Text.Contains("{{"))
                {
                    string originalText = text.Text;
                    string newText = originalText;

                    foreach (var placeholder in placeholders)
                    {
                        if (newText.Contains(placeholder.Key))
                        {
                            newText = newText.Replace(placeholder.Key, placeholder.Value);
                        }
                    }

                    if (newText != originalText)
                    {
                        text.Text = newText;
                    }
                }
            }

            // Try a simpler approach for handling split placeholders
            ReplaceSplitPlaceholdersSimpleApproach(document, placeholders);

            // If placeholders still exist, try the more complex approach
            bool placeholdersExist = CheckIfPlaceholdersExist(document, placeholders);
            if (placeholdersExist)
            {
                ReplaceSplitPlaceholdersComplexApproach(document, placeholders);
            }
        }
        catch (Exception)
        {
            // If any exception occurs, fall back to the simplest approach
            ReplaceSplitPlaceholdersSimpleApproach(document, placeholders);
        }
    }

    /// <summary>
    /// Checks if any placeholders still exist in the document
    /// </summary>
    private static bool CheckIfPlaceholdersExist(Document document, Dictionary<string, string> placeholders)
    {
        // Combine all text in the document
        string allText = string.Join("", document.Descendants<Text>().Select(t => t.Text));

        // Check if any placeholder exists
        foreach (var placeholder in placeholders.Keys)
        {
            if (allText.Contains(placeholder))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Simple approach to replace placeholders that might be split across multiple Text elements
    /// </summary>
    private static void ReplaceSplitPlaceholdersSimpleApproach(Document document, Dictionary<string, string> placeholders)
    {
        // Process each paragraph
        foreach (var paragraph in document.Descendants<Paragraph>())
        {
            // Get all text elements in this paragraph
            var textElements = paragraph.Descendants<Text>().ToList();
            if (textElements.Count <= 1) continue;

            // Combine all text in the paragraph
            string combinedText = string.Join("", textElements.Select(t => t.Text));

            // Check if the combined text contains any placeholder
            bool containsPlaceholder = false;
            foreach (var placeholderKey in placeholders.Keys)
            {
                if (combinedText.Contains(placeholderKey))
                {
                    containsPlaceholder = true;
                    break;
                }
            }

            if (!containsPlaceholder) continue;

            // Replace placeholders in the combined text
            string newCombinedText = combinedText;
            foreach (var placeholder in placeholders)
            {
                if (newCombinedText.Contains(placeholder.Key))
                {
                    newCombinedText = newCombinedText.Replace(placeholder.Key, placeholder.Value);
                }
            }

            // If the text changed, update the paragraph
            if (newCombinedText != combinedText)
            {
                // Clear all existing text elements
                foreach (var text in textElements)
                {
                    text.Text = "";
                }

                // Add the new text to the first element
                if (textElements.Count > 0)
                {
                    textElements[0].Text = newCombinedText;
                }
            }
        }
    }

    /// <summary>
    /// Complex approach to replace placeholders that might be split across multiple Text elements
    /// </summary>
    private static void ReplaceSplitPlaceholdersComplexApproach(Document document, Dictionary<string, string> placeholders)
    {
        // Process each paragraph
        foreach (var paragraph in document.Descendants<Paragraph>())
        {
            // Get all text elements in this paragraph
            var textElements = paragraph.Descendants<Text>().ToList();
            if (textElements.Count <= 1) continue;

            // Combine all text in the paragraph to check for placeholders
            string combinedText = string.Join("", textElements.Select(t => t.Text));

            // Check if the combined text contains any placeholder
            bool containsPlaceholder = false;
            foreach (var placeholderKey in placeholders.Keys)
            {
                if (combinedText.Contains(placeholderKey))
                {
                    containsPlaceholder = true;
                    break;
                }
            }

            if (!containsPlaceholder) continue;

            // Process each placeholder that might be split across multiple Text elements
            foreach (var placeholder in placeholders)
            {
                if (!combinedText.Contains(placeholder.Key)) continue;

                // Find the starting and ending Text elements for this placeholder
                int placeholderStart = combinedText.IndexOf(placeholder.Key);
                int placeholderEnd = placeholderStart + placeholder.Key.Length - 1;

                int currentPosition = 0;
                int startElementIndex = -1;
                int endElementIndex = -1;

                // Find which Text elements contain the start and end of the placeholder
                for (int i = 0; i < textElements.Count; i++)
                {
                    int textLength = textElements[i].Text.Length;

                    if (startElementIndex == -1 && placeholderStart >= currentPosition && placeholderStart < currentPosition + textLength)
                    {
                        startElementIndex = i;
                    }

                    if (endElementIndex == -1 && placeholderEnd >= currentPosition && placeholderEnd < currentPosition + textLength)
                    {
                        endElementIndex = i;
                        break;
                    }

                    currentPosition += textLength;
                }

                // If we found both the start and end elements
                if (startElementIndex != -1 && endElementIndex != -1)
                {
                    // Handle the case where the placeholder is split across multiple Text elements
                    if (startElementIndex == endElementIndex)
                    {
                        // Placeholder is within a single Text element (already handled above, but we'll do it again for completeness)
                        textElements[startElementIndex].Text = textElements[startElementIndex].Text.Replace(placeholder.Key, placeholder.Value);
                    }
                    else
                    {
                        // Calculate positions within the start and end elements
                        int startPos = placeholderStart - currentPosition + textElements[startElementIndex].Text.Length * startElementIndex;
                        int startElementPlaceholderPos = startPos - (currentPosition - textElements[startElementIndex].Text.Length);

                        // Replace the part of the placeholder in the start element
                        string startText = textElements[startElementIndex].Text;
                        textElements[startElementIndex].Text = string.Concat(startText.AsSpan(0, startElementPlaceholderPos), placeholder.Value);

                        // Remove the placeholder parts from middle elements
                        for (int i = startElementIndex + 1; i < endElementIndex; i++)
                        {
                            textElements[i].Text = "";
                        }

                        // Remove the part of the placeholder from the end element
                        string endText = textElements[endElementIndex].Text;
                        int endElementPlaceholderEndPos = placeholderEnd - (currentPosition - textElements[endElementIndex].Text.Length) + 1;
                        if (endElementPlaceholderEndPos < endText.Length)
                        {
                            textElements[endElementIndex].Text = endText[endElementPlaceholderEndPos..];
                        }
                        else
                        {
                            textElements[endElementIndex].Text = "";
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// Specifically handles replacing text in table cells, which might have different formatting
    /// </summary>
    private static void ReplaceTextInTableCells(Document document, Dictionary<string, string> placeholders)
    {
        // Find all tables in the document
        var tables = document.Descendants<Table>().ToList();

        foreach (var table in tables)
        {
            // Process each cell in the table
            foreach (var cell in table.Descendants<TableCell>())
            {
                // Get all text in this cell
                var textElements = cell.Descendants<Text>().ToList();
                if (textElements.Count == 0) continue;

                // Combine all text in the cell
                string combinedText = string.Join("", textElements.Select(t => t.Text));

                // Check if the combined text contains any placeholder
                bool containsPlaceholder = false;
                foreach (var placeholder in placeholders.Keys)
                {
                    if (combinedText.Contains(placeholder))
                    {
                        containsPlaceholder = true;
                        break;
                    }
                }

                if (!containsPlaceholder) continue;

                // Replace placeholders in the combined text
                string newCombinedText = combinedText;
                foreach (var placeholder in placeholders)
                {
                    if (newCombinedText.Contains(placeholder.Key))
                    {
                        newCombinedText = newCombinedText.Replace(placeholder.Key, placeholder.Value);
                    }
                }

                // If the text changed, update the cell
                if (newCombinedText != combinedText)
                {
                    // If there's only one text element, just update it
                    if (textElements.Count == 1)
                    {
                        textElements[0].Text = newCombinedText;
                    }
                    else
                    {
                        // Otherwise, clear all text elements and add the new text to the first one
                        foreach (var text in textElements)
                        {
                            text.Text = "";
                        }

                        if (textElements.Count > 0)
                        {
                            textElements[0].Text = newCombinedText;
                        }
                        else
                        {
                            // If there are no text elements, create a new one
                            var paragraph = cell.Descendants<Paragraph>().FirstOrDefault();
                            if (paragraph != null)
                            {
                                var run = new Run(new Text(newCombinedText));
                                paragraph.AppendChild(run);
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// Handles the case where the template might have multiple rows for each category
    /// </summary>
    private void HandleMultipleRowsInTables(Document document, InvoiceTemplateDataDto data)
    {
        // Find all tables in the document
        var tables = document.Descendants<Table>().ToList();
        foreach (var table in tables)
        {
            // Get all rows in the table
            var rows = table.Descendants<TableRow>().ToList();

            // Skip tables with too few rows
            if (rows.Count < 3)
            {
                continue;
            }

            // Check if this is the consumption table by looking for characteristic cell content
            bool isConsumptionTable = false;
            foreach (var row in rows)
            {
                var cells = row.Descendants<TableCell>().ToList();
                if (cells.Count < 3) continue;

                string cellText = string.Join("", cells[0].Descendants<Text>().Select(t => t.Text));

                if (cellText.Contains("Room Charges") || cellText.Contains("客房费用"))
                {
                    isConsumptionTable = true;
                    break;
                }
            }

            if (!isConsumptionTable)
            {
                continue;
            }

            // Get payment details from the data
            var roomDetails = data.PaymentDetails
                .Where(d => d.SourceType == PaymentSourceType.ReservationRoom.ToString())
                .ToList();

            var mealDetails = data.PaymentDetails
                .Where(d => d.SourceType == PaymentSourceType.ReservationRoomFoodAndBeverage.ToString())
                .ToList();

            var otherDetails = data.PaymentDetails
                .Where(d => d.SourceType == PaymentSourceType.ReservationRoomService.ToString())
                .ToList();

            // Ensure we have the actual names for each detail
            foreach (var detail in roomDetails)
            {
                if (string.IsNullOrEmpty(detail.Description) && !Guid.Empty.Equals(detail.SourceId))
                {
                    try
                    {
                        var query = _reservationRoomRepository
                               .WithDetailsAsync(rr => rr.ReservationDetails!.Room!.RoomType)
                               .GetAwaiter().GetResult();

                        var reservationRoom = query
                            .FirstOrDefault(rr => rr.Id == detail.SourceId);

                        if (reservationRoom?.ReservationDetails?.Room?.RoomType != null)
                        {
                            detail.Description = reservationRoom.ReservationDetails.Room.RoomType.Name;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error getting room type name");
                    }
                }
            }

            foreach (var detail in mealDetails)
            {
                if (string.IsNullOrEmpty(detail.Description) && !Guid.Empty.Equals(detail.SourceId))
                {
                    try
                    {
                        var query = _reservationFoodAndBeverageRepository
                            .WithDetailsAsync(rfb => rfb.FoodAndBeverage!)
                            .GetAwaiter().GetResult();

                        var reservationFoodAndBeverage = query
                            .FirstOrDefault(rfb => rfb.Id == detail.SourceId);

                        if (reservationFoodAndBeverage?.FoodAndBeverage != null)
                        {
                            detail.Description = reservationFoodAndBeverage.FoodAndBeverage.Name;
                        }

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error getting food and beverage name");
                    }
                }
            }

            foreach (var detail in otherDetails)
            {
                if (string.IsNullOrEmpty(detail.Description) && !Guid.Empty.Equals(detail.SourceId))
                {
                    try
                    {
                        // For services, we need to get the service directly
                        // Since we're dealing with a payment for a service, we can get the service ID from the source ID
                        var serviceDetails = _serviceRepository
                            .GetAsync(detail.SourceId)
                            .GetAwaiter().GetResult();

                        if (serviceDetails != null)
                        {
                            detail.Description = serviceDetails.Name;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error getting service name");
                    }
                }
            }


            // IMPORTANT: If we have multiple other expense details, we need to make sure they're treated as separate items
            // This is crucial for displaying multiple rows in the invoice
            for (int i = 0; i < otherDetails.Count; i++)
            {
                // Add a unique identifier to each other expense detail to ensure they're treated as separate items
                // Also make a copy of each detail to ensure they're treated as separate objects
                var originalDetail = otherDetails[i];
                var newDetail = new InvoicePaymentDetailDto
                {
                    SourceId = originalDetail.SourceId,
                    SourceType = originalDetail.SourceType,
                    Description = $"{originalDetail.Description}_{i}",
                    Amount = originalDetail.Amount,
                    Qty = originalDetail.Qty
                };
                otherDetails[i] = newDetail;
            }

            // Find rows that might contain room, meal, and other expense placeholders
            TableRow? roomRow = null;
            TableRow? mealRow = null;
            TableRow? otherRow = null;

            // Dump all rows for debugging
            for (int i = 0; i < rows.Count; i++)
            {
                var row = rows[i];
                string rowText = string.Join("", row.Descendants<Text>().Select(t => t.Text));
            }

            // Try to find template rows by looking for specific patterns
            foreach (var row in rows)
            {
                string rowText = string.Join("", row.Descendants<Text>().Select(t => t.Text));

                // Look for actual data rows with real values (not placeholders)
                // For room charges, look for room-related text
                if (rowText.Contains("房型") || rowText.Contains("单价") || rowText.Contains("数量") || rowText.Contains("晚数") ||
                    (rowText.Contains("Room") && !rowText.Contains("Charges") && !rowText.Contains("Service")))
                {
                    roomRow = row;
                }
                // For meal expenses, look for food-related text
                else if (rowText.Contains("餐饮") || rowText.Contains("餐费") || rowText.Contains("食品") ||
                         rowText.Contains("Food") || rowText.Contains("Meal") || rowText.Contains("Beverage"))
                {
                    mealRow = row;
                }
                // For other expenses, look for service-related text
                else if (rowText.Contains("名称") || rowText.Contains("服务") || rowText.Contains("费用") ||
                         rowText.Contains("Service") || rowText.Contains("Other") ||
                         rowText.Contains("Room Service"))
                {
                    otherRow = row;
                }
                // Look for category headers as a fallback
                else if (rowText.Contains("客房费用") || rowText.Contains("Room Charges"))
                {
                    // Look for a data row after the header
                    int rowIndex = rows.IndexOf(row);
                    // Skip the header row and the column header row to get to the data row
                    if (rowIndex >= 0 && rowIndex + 2 < rows.Count)
                    {
                        // Try to find the first non-placeholder row
                        for (int i = rowIndex + 2; i < rows.Count; i++)
                        {
                            var candidateRow = rows[i];
                            string candidateText = string.Join("", candidateRow.Descendants<Text>().Select(t => t.Text));
                            if (!candidateText.Contains("{{") && !string.IsNullOrWhiteSpace(candidateText))
                            {
                                roomRow = candidateRow;
                                break;
                            }
                        }
                    }
                }
                // Look for meal expenses row
                else if (rowText.Contains("餐饮费用") || rowText.Contains("Meal Expenses"))
                {
                    // Look for a data row after the header
                    int rowIndex = rows.IndexOf(row);
                    // Skip the header row and the column header row to get to the data row
                    if (rowIndex >= 0 && rowIndex + 2 < rows.Count)
                    {
                        // Try to find the first non-placeholder row
                        for (int i = rowIndex + 2; i < rows.Count; i++)
                        {
                            var candidateRow = rows[i];
                            string candidateText = string.Join("", candidateRow.Descendants<Text>().Select(t => t.Text));
                            if (!candidateText.Contains("{{") && !string.IsNullOrWhiteSpace(candidateText))
                            {
                                mealRow = candidateRow;
                                break;
                            }
                        }
                    }
                }
                // Look for other expenses row
                else if (rowText.Contains("其他费用") || rowText.Contains("Others"))
                {
                    // Look for a data row after the header
                    int rowIndex = rows.IndexOf(row);
                    // Skip the header row and the column header row to get to the data row
                    if (rowIndex >= 0 && rowIndex + 2 < rows.Count)
                    {
                        // Try to find the first non-placeholder row
                        for (int i = rowIndex + 2; i < rows.Count; i++)
                        {
                            var candidateRow = rows[i];
                            string candidateText = string.Join("", candidateRow.Descendants<Text>().Select(t => t.Text));
                            if (!candidateText.Contains("{{") && !string.IsNullOrWhiteSpace(candidateText))
                            {
                                otherRow = candidateRow;
                                break;
                            }
                        }
                    }
                }

                // Look for column headers
                else if (rowText.Contains("房型") || rowText.Contains("单价") || rowText.Contains("间数") ||
                         rowText.Contains("RM_TY") || rowText.Contains("RM_PR") || rowText.Contains("RM_QT") ||
                         rowText.Contains("名称") || rowText.Contains("数量") || rowText.Contains("费用") ||
                         rowText.Contains("ML_TY") || rowText.Contains("ML_PR") || rowText.Contains("ML_QT") ||
                         rowText.Contains("OT_TY") || rowText.Contains("OT_PR") || rowText.Contains("OT_QT"))
                {
                    // The actual template row is the next row (the data row, not the header)
                    int rowIndex = rows.IndexOf(row);
                    if (rowIndex >= 0 && rowIndex + 1 < rows.Count)
                    {
                        // Use the same row for all three sections
                        var dataRow = rows[rowIndex + 1];

                        // Only set the rows if they haven't been set already
                        if (roomRow == null)
                        {
                            roomRow = dataRow;
                        }

                        if (mealRow == null)
                        {
                            mealRow = dataRow;
                        }

                        if (otherRow == null)
                        {
                            otherRow = dataRow;
                        }
                    }
                }

                // Also check for placeholders as a fallback
                if (rowText.Contains("{{RM_TY}}") || rowText.Contains("{{RM_PR}}"))
                {
                    roomRow = row;
                }
                else if (rowText.Contains("{{ML_TY}}") || rowText.Contains("{{ML_PR}}"))
                {
                    mealRow = row;
                }
                else if (rowText.Contains("{{OT_TY}}") || rowText.Contains("{{OT_PR}}"))
                {
                    otherRow = row;
                }
                // Look for column headers
                else if (rowText.Contains("房型") || rowText.Contains("单价") || rowText.Contains("间数") ||
                         rowText.Contains("RM_TY") || rowText.Contains("RM_PR") || rowText.Contains("RM_QT") ||
                         rowText.Contains("名称") || rowText.Contains("数量") || rowText.Contains("费用") ||
                         rowText.Contains("ML_TY") || rowText.Contains("ML_PR") || rowText.Contains("ML_QT") ||
                         rowText.Contains("OT_TY") || rowText.Contains("OT_PR") || rowText.Contains("OT_QT"))
                {
                    // The actual template row is the next row (the data row, not the header)
                    int rowIndex = rows.IndexOf(row);
                    if (rowIndex >= 0 && rowIndex + 1 < rows.Count)
                    {
                        // Use the same row for all three sections
                        var dataRow = rows[rowIndex + 1];

                        // Only set the rows if they haven't been set already
                        if (roomRow == null)
                        {
                            roomRow = dataRow;
                        }

                        if (mealRow == null)
                        {
                            mealRow = dataRow;
                        }

                        if (otherRow == null)
                        {
                            otherRow = dataRow;
                        }
                    }
                }
            }
            // We don't need to add placeholders to the room row if it's an actual data row
            // Instead, we'll create a clone of the row and use it as a template
            if (roomRow != null)
            {
                string rowText = string.Join("", roomRow.Descendants<Text>().Select(t => t.Text));

                // Find the cells in the row
                var cells = roomRow.Descendants<TableCell>().ToList();

                // Dump all cells for debugging
                for (int i = 0; i < cells.Count; i++)
                {
                    var cell = cells[i];
                    string cellText = string.Join("", cell.Descendants<Text>().Select(t => t.Text));
                }
            }

            // We don't need to add placeholders to the meal row if it's an actual data row
            // Instead, we'll create a clone of the row and use it as a template
            if (mealRow != null)
            {
                string rowText = string.Join("", mealRow.Descendants<Text>().Select(t => t.Text));

                // Find the cells in the row
                var cells = mealRow.Descendants<TableCell>().ToList();

                // Dump all cells for debugging
                for (int i = 0; i < cells.Count; i++)
                {
                    var cell = cells[i];
                    string cellText = string.Join("", cell.Descendants<Text>().Select(t => t.Text));
                }
            }

            // We don't need to add placeholders to the other row if it's an actual data row
            // Instead, we'll create a clone of the row and use it as a template
            if (otherRow != null)
            {
                string rowText = string.Join("", otherRow.Descendants<Text>().Select(t => t.Text));

                // Find the cells in the row
                var cells = otherRow.Descendants<TableCell>().ToList();

                // Dump all cells for debugging
                for (int i = 0; i < cells.Count; i++)
                {
                    var cell = cells[i];
                    string cellText = string.Join("", cell.Descendants<Text>().Select(t => t.Text));
                }
            }

            if (roomRow != null && roomDetails.Count > 0)
            {
                DuplicateRowsForDetails(table, roomRow, roomDetails, "RM");
            }

            if (mealRow != null && mealDetails.Count > 0)
            {
                for (int i = 0; i < mealDetails.Count; i++)
                {
                    var detail = mealDetails[i];

                    // Get the actual name and store it in the description
                    string actualName = GetActualName(detail, "ML");

                    // Only update if we got a meaningful name
                    if (!string.IsNullOrEmpty(actualName) && actualName != "餐饮")
                    {
                        detail.Description = actualName;
                    }
                }

                DuplicateRowsForDetails(table, mealRow, mealDetails, "ML");
            }

            if (otherRow != null && otherDetails.Count > 0)
            {
                for (int i = 0; i < otherDetails.Count; i++)
                {
                    var detail = otherDetails[i];

                    // Get the actual name and store it in the description
                    string actualName = GetActualName(detail, "OT");

                    // Only update if we got a meaningful name
                    if (!string.IsNullOrEmpty(actualName) && actualName != "名称")
                    {
                        detail.Description = actualName;
                    }
                }

                DuplicateRowsForDetails(table, otherRow, otherDetails, "OT");
            }

            // Add subtotal and grand total rows
            AddSubtotalRows(table, roomDetails, mealDetails, otherDetails);
        }
    }

    /// <summary>
    /// Gets the actual name of a room type, food and beverage item, or service from the database
    /// </summary>
    private string GetActualName(InvoicePaymentDetailDto detail, string prefix)
    {
        // If we already have a description that's not just the source ID, use it
        if (!string.IsNullOrEmpty(detail.Description) &&
            !detail.Description.Contains(detail.SourceId.ToString(), StringComparison.OrdinalIgnoreCase) &&
            !detail.Description.Contains("_"))
        {
            return detail.Description;
        }

        // If we don't have a source ID, we can't get the actual name
        if (Guid.Empty.Equals(detail.SourceId))
        {
            return prefix == "RM" ? "房型" : (prefix == "ML" ? "餐饮" : "名称");
        }

        var sourceId = detail.SourceId;

        try
        {
            if (prefix == "RM" && detail.SourceType == PaymentSourceType.ReservationRoom.ToString())
            {
                var query = _reservationRoomRepository
                    .WithDetailsAsync(rr => rr.ReservationDetails!.Room!.RoomType)
                    .GetAwaiter().GetResult();

                var reservationRoom = query
                    .FirstOrDefault(rr => rr.Id == sourceId);

                if (reservationRoom?.ReservationDetails?.Room?.RoomType != null)
                {
                    return reservationRoom.ReservationDetails.Room.RoomType.Name;
                }

                return "房型";
            }
            else if (prefix == "ML" && detail.SourceType == PaymentSourceType.ReservationRoomFoodAndBeverage.ToString())
            {
                var query = _reservationFoodAndBeverageRepository
                    .WithDetailsAsync(rfb => rfb.FoodAndBeverage!)
                    .GetAwaiter().GetResult();

                var reservationFoodAndBeverage = query
                    .FirstOrDefault(rfb => rfb.Id == sourceId);

                if (reservationFoodAndBeverage?.FoodAndBeverage != null)
                {
                    return reservationFoodAndBeverage.FoodAndBeverage.Name;
                }

                return "餐饮";
            }
            else if (prefix == "OT" && detail.SourceType == PaymentSourceType.ReservationRoomService.ToString())
            {
                var serviceDetails = _serviceRepository
                    .GetAsync(sourceId)
                    .GetAwaiter().GetResult();

                if (serviceDetails != null)
                {
                    return serviceDetails.Name;
                }

                return "名称";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting actual name for {Prefix} with SourceId {SourceId}", prefix, detail.SourceId);
        }

        // Default fallback values
        return prefix == "RM" ? "房型" : (prefix == "ML" ? "餐饮" : "名称");
    }

    /// <summary>
    /// Duplicates a template row for each detail item
    /// </summary>
    private void DuplicateRowsForDetails(Table table, TableRow templateRow, List<InvoicePaymentDetailDto> details, string prefix)
    {
        // Skip if there are no details
        if (details.Count == 0) return;

        // Log the template row content for debugging
        string templateRowText = string.Join("", templateRow.Descendants<Text>().Select(t => t.Text));

        var cells = templateRow.Descendants<TableCell>().ToList();
        for (int i = 0; i < cells.Count; i++)
        {
            var cell = cells[i];
            string cellText = string.Join("", cell.Descendants<Text>().Select(t => t.Text));
        }

        // Get the index of the template row
        int rowIndex = table.Descendants<TableRow>().ToList().IndexOf(templateRow);
        if (rowIndex < 0)
        {
            return;
        }

        // Keep track of the last row we inserted
        TableRow lastInsertedRow = templateRow;

        // Keep track of whether we've replaced the template row
        bool templateRowReplaced = false;

        // Create a clone of the template row for each detail (including the first one)
        for (int i = 0; i < details.Count; i++)
        {
            // For all details, we'll clone the template row
            // This ensures each detail gets its own row
            TableRow currentRow;
            if (i == 0)
            {
                // For the first detail, we can use the existing row
                // But let's clone it anyway to ensure each detail gets its own row
                currentRow = (TableRow)templateRow.CloneNode(true);

                // Insert the new row after the template row
                try
                {
                    // Replace the template row with our clone
                    table.InsertAfter(currentRow, templateRow);
                    table.RemoveChild(templateRow);
                    lastInsertedRow = currentRow;
                    templateRowReplaced = true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to replace template row with clone for first detail");
                    // Fall back to using the template row
                    currentRow = templateRow;
                }
            }
            else
            {
                // Clone the template row
                currentRow = (TableRow)templateRow.CloneNode(true);

                // Make sure the clone is not null
                if (currentRow == null)
                {
                    _logger.LogError("Failed to clone template row for detail {Index}", i + 1);
                    continue;
                }

                // Insert the new row after the last inserted row
                try
                {
                    table.InsertAfter(currentRow, lastInsertedRow);
                    lastInsertedRow = currentRow;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert new row for detail {Index}", i + 1);
                    continue;
                }
            }

            // Update placeholders in the row with the appropriate suffix
            string suffix = i == 0 ? "" : $"_{i + 1}";

            // Get the detail for this row
            var detail = details[i];

            // For ML_TY and OT_TY, get the actual name before processing placeholders
            string actualName = "";
            if (prefix == "ML" || prefix == "OT")
            {
                // IMPORTANT: For food and beverage items, we need to make sure we're getting the actual name
                // from the database, not just using the description
                actualName = GetActualName(detail, prefix);

                // If we got a generic name like "Food & Beverage", try harder to get the actual name
                if (actualName == "Food & Beverage" && prefix == "ML")
                {
                    // Try to parse the SourceId from the description if it contains an underscore
                    // This is from our earlier modification where we added a unique identifier
                    var sourceId = detail.SourceId;

                    try
                    {
                        // Try to get the food and beverage directly
                        var reservationFoodAndBeverage = _reservationFoodAndBeverageRepository
                            .FindAsync(sourceId).GetAwaiter().GetResult();

                        if (reservationFoodAndBeverage != null && reservationFoodAndBeverage.FoodAndBeverageId != Guid.Empty)
                        {
                            var foodAndBeverage = _foodAndBeverageRepository
                                .FindAsync(reservationFoodAndBeverage.FoodAndBeverageId).GetAwaiter().GetResult();

                            if (foodAndBeverage != null)
                            {
                                actualName = foodAndBeverage.Name;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error getting actual name on second attempt");
                    }
                }
            }

            // Update the cells directly with actual values instead of just replacing placeholders
            // This ensures the values appear in the document even if there are no placeholders

            // Get all cells in the row
            var rowCells = currentRow.Descendants<TableCell>().ToList();

            // Update the cells based on the prefix
            if (prefix == "RM")
            {
                // For room charges, update the room type, price, quantity, and total
                if (rowCells.Count >= 4)
                {
                    // First cell: Room type (index 0)
                    UpdateCellText(rowCells[0], GetActualName(detail, prefix));

                    // Second cell: Price (index 1)
                    decimal unitPrice = detail.Amount;
                    if (detail.Qty > 0)
                    {
                        unitPrice = detail.Amount / detail.Qty;
                    }
                    UpdateCellText(rowCells[1], FormatCurrency(unitPrice));

                    // Third cell: Quantity (index 2)
                    UpdateCellText(rowCells[2], detail.Qty.ToString("0"));

                    // Fourth cell: Total (index 3)
                    UpdateCellText(rowCells[3], FormatCurrency(detail.Amount));
                }
            }
            else if (prefix == "ML")
            {
                // For meal expenses, update the meal type, quantity, price, and total
                if (rowCells.Count >= 8)
                {
                    // Fifth cell: Meal type (index 4)
                    UpdateCellText(rowCells[4], actualName);

                    // Sixth cell: Quantity (index 5)
                    UpdateCellText(rowCells[5], detail.Qty.ToString("0"));

                    // Seventh cell: Price (index 6)
                    decimal unitPrice = detail.Amount;
                    if (detail.Qty > 0)
                    {
                        unitPrice = detail.Amount / detail.Qty;
                    }
                    UpdateCellText(rowCells[6], FormatCurrency(unitPrice));

                    // Eighth cell: Total (index 7)
                    UpdateCellText(rowCells[7], FormatCurrency(detail.Amount));
                }
            }
            else if (prefix == "OT")
            {
                // For other expenses, update the service type, quantity, price, and total
                if (rowCells.Count >= 12)
                {
                    // Ninth cell: Service type (index 8)
                    UpdateCellText(rowCells[8], actualName);

                    // Tenth cell: Quantity (index 9)
                    UpdateCellText(rowCells[9], detail.Qty.ToString("0"));

                    // Eleventh cell: Price (index 10)
                    decimal unitPrice = detail.Amount;
                    if (detail.Qty > 0)
                    {
                        unitPrice = detail.Amount / detail.Qty;
                    }
                    UpdateCellText(rowCells[10], FormatCurrency(unitPrice));

                    // Twelfth cell: Total (index 11)
                    UpdateCellText(rowCells[11], FormatCurrency(detail.Amount));
                }
            }

            // Also update any placeholders in the text
            foreach (var text in currentRow.Descendants<Text>())
            {
                string originalText = text.Text;
                // Replace placeholders with indexed versions or actual values
                if (originalText.Contains($"{{{{{prefix}_TY}}}}"))
                {
                    // For ML_TY and OT_TY, we want to directly replace with the actual name
                    if (prefix == "ML" || prefix == "OT")
                    {
                        text.Text = originalText.Replace($"{{{{{prefix}_TY}}}}", actualName);
                    }
                    else
                    {
                        // For RM_TY, use the actual room type
                        text.Text = originalText.Replace($"{{{{{prefix}_TY}}}}", GetActualName(detail, prefix));
                    }
                }
                else if (originalText.Contains($"{{{{{prefix}_PR}}}}"))
                {
                    // Replace with actual price
                    decimal unitPrice = detail.Amount;
                    if (detail.Qty > 0)
                    {
                        unitPrice = detail.Amount / detail.Qty;
                    }
                    text.Text = originalText.Replace($"{{{{{prefix}_PR}}}}", FormatCurrency(unitPrice));
                }
                else if (originalText.Contains($"{{{{{prefix}_QT}}}}"))
                {
                    // Replace with actual quantity
                    text.Text = originalText.Replace($"{{{{{prefix}_QT}}}}", detail.Qty.ToString("0"));
                }
                else if (originalText.Contains($"{{{{{prefix}_NI}}}}"))
                {
                    // Replace with nights (same as quantity for rooms)
                    text.Text = originalText.Replace($"{{{{{prefix}_NI}}}}", detail.Qty.ToString("0"));
                }
                else if (originalText.Contains($"{{{{{prefix}_AM}}}}"))
                {
                    // Replace with actual amount
                    text.Text = originalText.Replace($"{{{{{prefix}_AM}}}}", FormatCurrency(detail.Amount));
                }
            }
        }

        // If we didn't replace the template row, we need to update it with the values from the first detail
        if (!templateRowReplaced && details.Count > 0)
        {
            // Get the first detail
            var firstDetail = details[0];

            // Get the actual name for the first detail
            string actualName = "";
            if (prefix == "ML" || prefix == "OT")
            {
                actualName = GetActualName(firstDetail, prefix);
            }

            // Update placeholders in the template row
            foreach (var text in templateRow.Descendants<Text>())
            {
                string originalText = text.Text;

                // Replace placeholders with actual values
                if (originalText.Contains($"{{{{{prefix}_TY}}}}"))
                {
                    // For ML_TY and OT_TY, we want to directly replace with the actual name
                    if (prefix == "ML" || prefix == "OT")
                    {
                        text.Text = originalText.Replace($"{{{{{prefix}_TY}}}}", actualName);
                    }
                }
            }
        }
    }

    /// <summary>
    /// Adds subtotal rows to the table for each section
    /// </summary>
    private void AddSubtotalRows(Table table, List<InvoicePaymentDetailDto> roomDetails, List<InvoicePaymentDetailDto> mealDetails, List<InvoicePaymentDetailDto> otherDetails)
    {
        // Calculate subtotals for each section
        decimal roomTotal = 0;
        decimal mealTotal = 0;
        decimal otherTotal = 0;

        // Calculate room total
        foreach (var detail in roomDetails)
        {
            roomTotal += detail.Amount;
        }

        // Calculate meal total
        foreach (var detail in mealDetails)
        {
            mealTotal += detail.Amount;
        }

        // Calculate other total
        foreach (var detail in otherDetails)
        {
            otherTotal += detail.Amount;
        }

        // Create subtotal rows for each section
        // First, create the room subtotal row
        var roomSubtotalRow = new TableRow();

        // Room subtotal label cell
        var roomSubtotalLabelCell = new TableCell();
        var roomSubtotalLabelParagraph = new Paragraph(new Run(new Text("小计：")));
        roomSubtotalLabelCell.Append(roomSubtotalLabelParagraph);
        roomSubtotalRow.Append(roomSubtotalLabelCell);

        // Room subtotal value cell
        var roomSubtotalValueCell = new TableCell();
        var roomSubtotalValueParagraph = new Paragraph(new Run(new Text(FormatCurrency(roomTotal))));
        roomSubtotalValueCell.Append(roomSubtotalValueParagraph);
        roomSubtotalValueCell.TableCellProperties = new TableCellProperties
        {
            GridSpan = new GridSpan { Val = 3 },
            TableCellVerticalAlignment = new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center }
        };
        roomSubtotalRow.Append(roomSubtotalValueCell);

        // Add empty cells for meal and other sections
        for (int i = 0; i < 8; i++)
        {
            var emptyCell = new TableCell();
            var emptyParagraph = new Paragraph(new Run(new Text("")));
            emptyCell.Append(emptyParagraph);
            roomSubtotalRow.Append(emptyCell);
        }

        // Add the room subtotal row to the table
        table.Append(roomSubtotalRow);

        // Create the meal subtotal row
        var mealSubtotalRow = new TableRow();

        // Add empty cells for room section
        for (int i = 0; i < 4; i++)
        {
            var emptyCell = new TableCell();
            var emptyParagraph = new Paragraph(new Run(new Text("")));
            emptyCell.Append(emptyParagraph);
            mealSubtotalRow.Append(emptyCell);
        }

        // Meal subtotal label cell
        var mealSubtotalLabelCell = new TableCell();
        var mealSubtotalLabelParagraph = new Paragraph(new Run(new Text("小计：")));
        mealSubtotalLabelCell.Append(mealSubtotalLabelParagraph);
        mealSubtotalRow.Append(mealSubtotalLabelCell);

        // Meal subtotal value cell
        var mealSubtotalValueCell = new TableCell();
        var mealSubtotalValueParagraph = new Paragraph(new Run(new Text(FormatCurrency(mealTotal))));
        mealSubtotalValueCell.Append(mealSubtotalValueParagraph);
        mealSubtotalValueCell.TableCellProperties = new TableCellProperties
        {
            GridSpan = new GridSpan { Val = 3 },
            TableCellVerticalAlignment = new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center }
        };
        mealSubtotalRow.Append(mealSubtotalValueCell);

        // Add empty cells for other section
        for (int i = 0; i < 4; i++)
        {
            var emptyCell = new TableCell();
            var emptyParagraph = new Paragraph(new Run(new Text("")));
            emptyCell.Append(emptyParagraph);
            mealSubtotalRow.Append(emptyCell);
        }

        // Add the meal subtotal row to the table
        table.Append(mealSubtotalRow);

        // Create the other subtotal row
        var otherSubtotalRow = new TableRow();

        // Add empty cells for room and meal sections
        for (int i = 0; i < 8; i++)
        {
            var emptyCell = new TableCell();
            var emptyParagraph = new Paragraph(new Run(new Text("")));
            emptyCell.Append(emptyParagraph);
            otherSubtotalRow.Append(emptyCell);
        }

        // Other subtotal label cell
        var otherSubtotalLabelCell = new TableCell();
        var otherSubtotalLabelParagraph = new Paragraph(new Run(new Text("小计：")));
        otherSubtotalLabelCell.Append(otherSubtotalLabelParagraph);
        otherSubtotalRow.Append(otherSubtotalLabelCell);

        // Other subtotal value cell
        var otherSubtotalValueCell = new TableCell();
        var otherSubtotalValueParagraph = new Paragraph(new Run(new Text(FormatCurrency(otherTotal))));
        otherSubtotalValueCell.Append(otherSubtotalValueParagraph);
        otherSubtotalValueCell.TableCellProperties = new TableCellProperties
        {
            GridSpan = new GridSpan { Val = 3 },
            TableCellVerticalAlignment = new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center }
        };
        otherSubtotalRow.Append(otherSubtotalValueCell);

        // Add the other subtotal row to the table
        table.Append(otherSubtotalRow);

        // Create grand total row
        var grandTotalRow = new TableRow();

        // Grand total label cell
        var grandTotalLabelCell = new TableCell();
        var grandTotalLabelParagraph = new Paragraph(new Run(new Text("总计 (Total)：")));
        grandTotalLabelCell.Append(grandTotalLabelParagraph);
        grandTotalLabelCell.TableCellProperties = new TableCellProperties
        {
            GridSpan = new GridSpan { Val = 11 },
            TableCellVerticalAlignment = new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center }
        };
        grandTotalRow.Append(grandTotalLabelCell);

        // Grand total value cell
        var grandTotalValueCell = new TableCell();
        var grandTotalValueParagraph = new Paragraph(new Run(new Text(FormatCurrency(roomTotal + mealTotal + otherTotal))));
        grandTotalValueCell.Append(grandTotalValueParagraph);
        grandTotalRow.Append(grandTotalValueCell);

        // Add the grand total row to the table
        table.Append(grandTotalRow);
    }

    private void UpdateCellText(TableCell cell, string newText)
    {
        var paragraph = cell.Descendants<Paragraph>().FirstOrDefault();
        if (paragraph != null)
        {
            var run = paragraph.Descendants<Run>().FirstOrDefault();
            if (run != null)
            {
                var text = run.Descendants<Text>().FirstOrDefault();
                if (text != null)
                {
                    text.Text = newText;
                }
                else
                {
                    // Create a new Text element
                    text = new Text(newText);
                    run.AppendChild(text);
                }
            }
            else
            {
                // Create a new Run element with Text
                run = new Run(new Text(newText));
                paragraph.AppendChild(run);
            }
        }
        else
        {
            // Create a new Paragraph with Run and Text
            paragraph = new Paragraph(new Run(new Text(newText)));
            cell.AppendChild(paragraph);
        }
    }

    /// <summary>
    /// Formats a decimal as currency with thousand separators
    /// </summary>
    private static string FormatCurrency(decimal amount)
    {
        // Format with thousand separators and no decimal places
        return amount.ToString("#,##0");
    }

    /// <summary>
    /// Ensures we have example data for the invoice if real data is not available
    /// </summary>
    private void EnsureDataForExample(
        List<InvoicePaymentDetailDto> roomDetails,
        List<InvoicePaymentDetailDto> mealDetails,
        List<InvoicePaymentDetailDto> otherDetails)
    {
        // Ensure we have at least one room detail
        if (roomDetails.Count == 0)
        {
            roomDetails.Add(new InvoicePaymentDetailDto
            {
                Description = "EXECUTIVE SUITE",
                Amount = 30000000,
                Qty = 15,
                SourceType = PaymentSourceType.ReservationRoom.ToString()
            });
        }

        // Ensure we have meal details
        if (mealDetails.Count == 0)
        {
            mealDetails.Add(new InvoicePaymentDetailDto
            {
                Description = "BUFFET",
                Amount = 990000,
                Qty = 3,
                SourceType = PaymentSourceType.ReservationRoomFoodAndBeverage.ToString()
            });

            mealDetails.Add(new InvoicePaymentDetailDto
            {
                Description = "STANDART B",
                Amount = 2200000,
                Qty = 2,
                SourceType = PaymentSourceType.ReservationRoomFoodAndBeverage.ToString()
            });

            mealDetails.Add(new InvoicePaymentDetailDto
            {
                Description = "DELIVERY",
                Amount = 1200000,
                Qty = 3,
                SourceType = PaymentSourceType.ReservationRoomFoodAndBeverage.ToString()
            });
        }

        // Ensure we have other details
        if (otherDetails.Count == 0)
        {
            otherDetails.Add(new InvoicePaymentDetailDto
            {
                Description = "JETSKI",
                Amount = 2200000,
                Qty = 1,
                SourceType = PaymentSourceType.ReservationRoomService.ToString()
            });

            otherDetails.Add(new InvoicePaymentDetailDto
            {
                Description = "Ruang Rapat Kecil",
                Amount = 880000,
                Qty = 1,
                SourceType = PaymentSourceType.ReservationRoomService.ToString()
            });
        }
    }
}