﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.TypeFoodAndBeverages;

[Authorize(WismaAppPermissions.PolicyServiceType.Default)]
public class TypeFoodAndBeverageAppService : CrudAppService<
        TypeFoodAndBeverage,
        TypeFoodAndBeverageDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateTypeFoodAndBeverageDto,
        CreateUpdateTypeFoodAndBeverageDto
    >, ITypeFoodAndBeverageAppService
{
    private readonly IRepository<TypeFoodAndBeverage, Guid> _repository;
    public TypeFoodAndBeverageAppService(IRepository<TypeFoodAndBeverage, Guid> repository, IPermissionChecker permissionChecker)
        : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<IQueryable<TypeFoodAndBeverage>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.Status);
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyServiceType.View)]
    public override Task<PagedResultDto<TypeFoodAndBeverageDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyServiceType.View)]
    public override async Task<TypeFoodAndBeverageDto> GetAsync(Guid id)
    {
        // await CheckGetPolicyAsync();

        var query = await Repository.GetQueryableAsync();
        var typeFoodAndBeverage = await query
            .Include(x => x.Status)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (typeFoodAndBeverage is null)
        {
            throw new EntityNotFoundException(typeof(TypeFoodAndBeverage), id);
        }

        return ObjectMapper.Map<TypeFoodAndBeverage, TypeFoodAndBeverageDto>(typeFoodAndBeverage);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyServiceType.Create)]
    public override async Task<TypeFoodAndBeverageDto> CreateAsync(CreateUpdateTypeFoodAndBeverageDto input)
    {
        return await base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyServiceType.Edit)]
    public override async Task<TypeFoodAndBeverageDto> UpdateAsync(Guid id, CreateUpdateTypeFoodAndBeverageDto input)
    {
        return await base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyServiceType.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }

}